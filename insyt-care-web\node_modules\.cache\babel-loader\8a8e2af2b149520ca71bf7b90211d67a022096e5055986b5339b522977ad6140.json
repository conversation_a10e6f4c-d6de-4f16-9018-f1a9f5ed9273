{"ast": null, "code": "export const preventDefault = () => {\n  document.querySelectorAll(\"form\").forEach(form => {\n    form.addEventListener(\"submit\", e => e.preventDefault());\n  });\n  document.querySelectorAll('a[href=\"#\"]').forEach(a => {\n    a.addEventListener(\"click\", e => e.preventDefault());\n  });\n};\nexport function generateAlphabet() {\n  return [...Array(26)].map((_, i) => String.fromCharCode(i + 97));\n}\nexport const convertToDataURL = async file => {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.onload = () => resolve(reader.result);\n    reader.onerror = error => reject(error);\n    reader.readAsDataURL(file);\n  });\n};\nexport function formatBytes(bytes, decimals = 2) {\n  if (bytes === 0) return \"0 Bytes\";\n  const k = 1024;\n  const dm = decimals < 0 ? 0 : decimals;\n  const sizes = [\"Bytes\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\", \"EB\", \"ZB\", \"YB\"];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + \" \" + sizes[i];\n}\nexport function generateRandomString(length = 3) {\n  const characters = \"abcdefghijklmnopqrstuvwxyz123456789\";\n  let result = \"\";\n  for (let i = 0; i < length; i++) {\n    result += characters.charAt(Math.floor(Math.random() * characters.length));\n  }\n  return result;\n}\nexport function getNameInitials(...names) {\n  // Filter out empty strings and get first letter of each name\n  const initials = names.filter(name => name && typeof name === \"string\").map(name => name.trim().charAt(0).toUpperCase());\n\n  // If we have at least 2 initials, return first 2\n  if (initials.length >= 2) {\n    return initials.slice(0, 2).join(\"\");\n  }\n\n  // If we have only 1 initial, return it\n  if (initials.length === 1) {\n    return initials[0];\n  }\n\n  // If no valid names provided, return empty string\n  return \"\";\n}\nexport function generateNames(name) {\n  var _nameParts$slice;\n  if (!name) return {\n    firstName: \"\",\n    lastName: \"\"\n  };\n  const nameParts = name === null || name === void 0 ? void 0 : name.trim().split(\" \");\n  const firstName = nameParts[0] || \"\";\n  const lastName = (nameParts === null || nameParts === void 0 ? void 0 : (_nameParts$slice = nameParts.slice(1)) === null || _nameParts$slice === void 0 ? void 0 : _nameParts$slice.join(\" \")) || \"\";\n  return {\n    firstName,\n    lastName\n  };\n}\n\n/**\r\n * Formats an address object into a properly structured display format\r\n * @param {Object} address - Address object with components\r\n * @returns {string} Formatted address string with proper line breaks\r\n */\nexport function formatAddress(address) {\n  if (!address) return \"\";\n\n  // If formattedAddress exists and looks properly formatted, use it\n  if (address.formattedAddress && !address.formattedAddress.includes(',')) {\n    return address.formattedAddress;\n  }\n\n  // Build address components\n  const streetParts = [address.streetNumber, address.streetName, address.addressLine2].filter(Boolean);\n  const locationParts = [address.city, address.state, address.country].filter(Boolean);\n\n  // Create formatted address with proper structure\n  const addressLines = [];\n\n  // Add street address (if available)\n  if (streetParts.length > 0) {\n    addressLines.push(streetParts.join(' '));\n  }\n\n  // Add city, state, country (if available)\n  if (locationParts.length > 0) {\n    addressLines.push(locationParts.join(', '));\n  }\n\n  // If no components available, fall back to formattedAddress\n  if (addressLines.length === 0 && address.formattedAddress) {\n    return address.formattedAddress;\n  }\n  return addressLines.join(',\\n');\n}", "map": {"version": 3, "names": ["preventDefault", "document", "querySelectorAll", "for<PERSON>ach", "form", "addEventListener", "e", "a", "generateAlphabet", "Array", "map", "_", "i", "String", "fromCharCode", "convertToDataURL", "file", "Promise", "resolve", "reject", "reader", "FileReader", "onload", "result", "onerror", "error", "readAsDataURL", "formatBytes", "bytes", "decimals", "k", "dm", "sizes", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "generateRandomString", "length", "characters", "char<PERSON>t", "random", "getNameInitials", "names", "initials", "filter", "name", "trim", "toUpperCase", "slice", "join", "generateNames", "_nameParts$slice", "firstName", "lastName", "nameParts", "split", "formatAddress", "address", "formattedAddress", "includes", "streetParts", "streetNumber", "streetName", "addressLine2", "Boolean", "locationParts", "city", "state", "country", "addressLines", "push"], "sources": ["D:/Softwares/insyt-care/insyt-care-web/src/utils/helpers.js"], "sourcesContent": ["export const preventDefault = () => {\r\n  document.querySelectorAll(\"form\").forEach((form) => {\r\n    form.addEventListener(\"submit\", (e) => e.preventDefault());\r\n  });\r\n  document.querySelectorAll('a[href=\"#\"]').forEach((a) => {\r\n    a.addEventListener(\"click\", (e) => e.preventDefault());\r\n  });\r\n};\r\n\r\nexport function generateAlphabet() {\r\n  return [...Array(26)].map((_, i) => String.fromCharCode(i + 97));\r\n}\r\n\r\nexport const convertToDataURL = async (file) => {\r\n  return new Promise((resolve, reject) => {\r\n    const reader = new FileReader();\r\n    reader.onload = () => resolve(reader.result);\r\n    reader.onerror = (error) => reject(error);\r\n    reader.readAsDataURL(file);\r\n  });\r\n};\r\n\r\nexport function formatBytes(bytes, decimals = 2) {\r\n  if (bytes === 0) return \"0 Bytes\";\r\n  const k = 1024;\r\n  const dm = decimals < 0 ? 0 : decimals;\r\n  const sizes = [\"Bytes\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\", \"EB\", \"ZB\", \"YB\"];\r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + \" \" + sizes[i];\r\n}\r\n\r\nexport function generateRandomString(length = 3) {\r\n  const characters = \"abcdefghijklmnopqrstuvwxyz123456789\";\r\n  let result = \"\";\r\n  for (let i = 0; i < length; i++) {\r\n    result += characters.charAt(Math.floor(Math.random() * characters.length));\r\n  }\r\n  return result;\r\n}\r\n\r\nexport function getNameInitials(...names) {\r\n  // Filter out empty strings and get first letter of each name\r\n  const initials = names\r\n    .filter((name) => name && typeof name === \"string\")\r\n    .map((name) => name.trim().charAt(0).toUpperCase());\r\n\r\n  // If we have at least 2 initials, return first 2\r\n  if (initials.length >= 2) {\r\n    return initials.slice(0, 2).join(\"\");\r\n  }\r\n\r\n  // If we have only 1 initial, return it\r\n  if (initials.length === 1) {\r\n    return initials[0];\r\n  }\r\n\r\n  // If no valid names provided, return empty string\r\n  return \"\";\r\n}\r\n\r\nexport function generateNames(name) {\r\n  if (!name) return { firstName: \"\", lastName: \"\" };\r\n  const nameParts = name?.trim().split(\" \");\r\n  const firstName = nameParts[0] || \"\";\r\n  const lastName = nameParts?.slice(1)?.join(\" \") || \"\";\r\n  return { firstName, lastName };\r\n}\r\n\r\n/**\r\n * Formats an address object into a properly structured display format\r\n * @param {Object} address - Address object with components\r\n * @returns {string} Formatted address string with proper line breaks\r\n */\r\nexport function formatAddress(address) {\r\n  if (!address) return \"\";\r\n\r\n  // If formattedAddress exists and looks properly formatted, use it\r\n  if (address.formattedAddress && !address.formattedAddress.includes(',')) {\r\n    return address.formattedAddress;\r\n  }\r\n\r\n  // Build address components\r\n  const streetParts = [\r\n    address.streetNumber,\r\n    address.streetName,\r\n    address.addressLine2\r\n  ].filter(Boolean);\r\n\r\n  const locationParts = [\r\n    address.city,\r\n    address.state,\r\n    address.country\r\n  ].filter(Boolean);\r\n\r\n  // Create formatted address with proper structure\r\n  const addressLines = [];\r\n\r\n  // Add street address (if available)\r\n  if (streetParts.length > 0) {\r\n    addressLines.push(streetParts.join(' '));\r\n  }\r\n\r\n  // Add city, state, country (if available)\r\n  if (locationParts.length > 0) {\r\n    addressLines.push(locationParts.join(', '));\r\n  }\r\n\r\n  // If no components available, fall back to formattedAddress\r\n  if (addressLines.length === 0 && address.formattedAddress) {\r\n    return address.formattedAddress;\r\n  }\r\n\r\n  return addressLines.join(',\\n');\r\n}\r\n"], "mappings": "AAAA,OAAO,MAAMA,cAAc,GAAGA,CAAA,KAAM;EAClCC,QAAQ,CAACC,gBAAgB,CAAC,MAAM,CAAC,CAACC,OAAO,CAAEC,IAAI,IAAK;IAClDA,IAAI,CAACC,gBAAgB,CAAC,QAAQ,EAAGC,CAAC,IAAKA,CAAC,CAACN,cAAc,CAAC,CAAC,CAAC;EAC5D,CAAC,CAAC;EACFC,QAAQ,CAACC,gBAAgB,CAAC,aAAa,CAAC,CAACC,OAAO,CAAEI,CAAC,IAAK;IACtDA,CAAC,CAACF,gBAAgB,CAAC,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACN,cAAc,CAAC,CAAC,CAAC;EACxD,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,SAASQ,gBAAgBA,CAAA,EAAG;EACjC,OAAO,CAAC,GAAGC,KAAK,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKC,MAAM,CAACC,YAAY,CAACF,CAAC,GAAG,EAAE,CAAC,CAAC;AAClE;AAEA,OAAO,MAAMG,gBAAgB,GAAG,MAAOC,IAAI,IAAK;EAC9C,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,MAAM,GAAG,MAAMJ,OAAO,CAACE,MAAM,CAACG,MAAM,CAAC;IAC5CH,MAAM,CAACI,OAAO,GAAIC,KAAK,IAAKN,MAAM,CAACM,KAAK,CAAC;IACzCL,MAAM,CAACM,aAAa,CAACV,IAAI,CAAC;EAC5B,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,SAASW,WAAWA,CAACC,KAAK,EAAEC,QAAQ,GAAG,CAAC,EAAE;EAC/C,IAAID,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;EACjC,MAAME,CAAC,GAAG,IAAI;EACd,MAAMC,EAAE,GAAGF,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAGA,QAAQ;EACtC,MAAMG,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACvE,MAAMpB,CAAC,GAAGqB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACP,KAAK,CAAC,GAAGK,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC;EACnD,OAAOM,UAAU,CAAC,CAACR,KAAK,GAAGK,IAAI,CAACI,GAAG,CAACP,CAAC,EAAElB,CAAC,CAAC,EAAE0B,OAAO,CAACP,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGC,KAAK,CAACpB,CAAC,CAAC;AAC1E;AAEA,OAAO,SAAS2B,oBAAoBA,CAACC,MAAM,GAAG,CAAC,EAAE;EAC/C,MAAMC,UAAU,GAAG,qCAAqC;EACxD,IAAIlB,MAAM,GAAG,EAAE;EACf,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,MAAM,EAAE5B,CAAC,EAAE,EAAE;IAC/BW,MAAM,IAAIkB,UAAU,CAACC,MAAM,CAACT,IAAI,CAACC,KAAK,CAACD,IAAI,CAACU,MAAM,CAAC,CAAC,GAAGF,UAAU,CAACD,MAAM,CAAC,CAAC;EAC5E;EACA,OAAOjB,MAAM;AACf;AAEA,OAAO,SAASqB,eAAeA,CAAC,GAAGC,KAAK,EAAE;EACxC;EACA,MAAMC,QAAQ,GAAGD,KAAK,CACnBE,MAAM,CAAEC,IAAI,IAAKA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,CAAC,CAClDtC,GAAG,CAAEsC,IAAI,IAAKA,IAAI,CAACC,IAAI,CAAC,CAAC,CAACP,MAAM,CAAC,CAAC,CAAC,CAACQ,WAAW,CAAC,CAAC,CAAC;;EAErD;EACA,IAAIJ,QAAQ,CAACN,MAAM,IAAI,CAAC,EAAE;IACxB,OAAOM,QAAQ,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EACtC;;EAEA;EACA,IAAIN,QAAQ,CAACN,MAAM,KAAK,CAAC,EAAE;IACzB,OAAOM,QAAQ,CAAC,CAAC,CAAC;EACpB;;EAEA;EACA,OAAO,EAAE;AACX;AAEA,OAAO,SAASO,aAAaA,CAACL,IAAI,EAAE;EAAA,IAAAM,gBAAA;EAClC,IAAI,CAACN,IAAI,EAAE,OAAO;IAAEO,SAAS,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAG,CAAC;EACjD,MAAMC,SAAS,GAAGT,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,IAAI,CAAC,CAAC,CAACS,KAAK,CAAC,GAAG,CAAC;EACzC,MAAMH,SAAS,GAAGE,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE;EACpC,MAAMD,QAAQ,GAAG,CAAAC,SAAS,aAATA,SAAS,wBAAAH,gBAAA,GAATG,SAAS,CAAEN,KAAK,CAAC,CAAC,CAAC,cAAAG,gBAAA,uBAAnBA,gBAAA,CAAqBF,IAAI,CAAC,GAAG,CAAC,KAAI,EAAE;EACrD,OAAO;IAAEG,SAAS;IAAEC;EAAS,CAAC;AAChC;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,aAAaA,CAACC,OAAO,EAAE;EACrC,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;;EAEvB;EACA,IAAIA,OAAO,CAACC,gBAAgB,IAAI,CAACD,OAAO,CAACC,gBAAgB,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;IACvE,OAAOF,OAAO,CAACC,gBAAgB;EACjC;;EAEA;EACA,MAAME,WAAW,GAAG,CAClBH,OAAO,CAACI,YAAY,EACpBJ,OAAO,CAACK,UAAU,EAClBL,OAAO,CAACM,YAAY,CACrB,CAACnB,MAAM,CAACoB,OAAO,CAAC;EAEjB,MAAMC,aAAa,GAAG,CACpBR,OAAO,CAACS,IAAI,EACZT,OAAO,CAACU,KAAK,EACbV,OAAO,CAACW,OAAO,CAChB,CAACxB,MAAM,CAACoB,OAAO,CAAC;;EAEjB;EACA,MAAMK,YAAY,GAAG,EAAE;;EAEvB;EACA,IAAIT,WAAW,CAACvB,MAAM,GAAG,CAAC,EAAE;IAC1BgC,YAAY,CAACC,IAAI,CAACV,WAAW,CAACX,IAAI,CAAC,GAAG,CAAC,CAAC;EAC1C;;EAEA;EACA,IAAIgB,aAAa,CAAC5B,MAAM,GAAG,CAAC,EAAE;IAC5BgC,YAAY,CAACC,IAAI,CAACL,aAAa,CAAChB,IAAI,CAAC,IAAI,CAAC,CAAC;EAC7C;;EAEA;EACA,IAAIoB,YAAY,CAAChC,MAAM,KAAK,CAAC,IAAIoB,OAAO,CAACC,gBAAgB,EAAE;IACzD,OAAOD,OAAO,CAACC,gBAAgB;EACjC;EAEA,OAAOW,YAAY,CAACpB,IAAI,CAAC,KAAK,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}