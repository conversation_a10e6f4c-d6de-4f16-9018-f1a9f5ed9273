import { db } from "config/firebase.config";
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { doc, getDoc } from "firebase/firestore";
import { COLLECTIONS } from "@constants/app";

const initialState = {
  error: null,
  isLoading: false,
  user: null,
};

// THUNK TO GET LOGGED IN USER
export const getLoggedInUser = createAsyncThunk("auth/getLoggedInUser", async (userId, { rejectWithValue }) => {
  if (userId) {
    try {
      const docSnapshot = await getDoc(doc(db, COLLECTIONS.USERS, userId));
      if (!docSnapshot.exists()) {
        return rejectWithValue("User data not doesn't exists");
      }
      const user = docSnapshot.data();
      return {
        id: userId,
        ...user,
      };
    } catch (error) {
      return rejectWithValue(error);
    }
  }
});

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    updateAuthUserAction: (state, { payload }) => {
      if (payload) {
        state.user = { ...state.user, ...payload };
      } else if (payload === null) {
        state.user = null;
      }
      return state;
    },
  },
  extraReducers: (builder) => {
    builder
      // GET USER BY ID
      .addCase(getLoggedInUser.pending, (state) => {
        state.error = null;
        state.isLoading = true;
      })
      .addCase(getLoggedInUser.fulfilled, (state, { payload }) => {
        state.user = payload;
        state.isLoading = false;
      })
      .addCase(getLoggedInUser.rejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
        state.user = null;
        // Clear localStorage when user data fetch fails
        localStorage.removeItem("userId");
        localStorage.removeItem("access_token");
      });
  },
});

export const { updateAuthUserAction } = authSlice.actions;

export default authSlice;
