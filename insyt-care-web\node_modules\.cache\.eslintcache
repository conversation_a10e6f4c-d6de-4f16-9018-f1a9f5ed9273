[{"D:\\Softwares\\insyt-care\\insyt-care-web\\src\\index.jsx": "1", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\App.jsx": "2", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\store.js": "3", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\contexts\\interfaceContext.js": "4", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\contexts\\sidebarContext.js": "5", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\AppLayout.jsx": "6", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\styles\\global.js": "7", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\helpers.js": "8", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\styles\\mui-theme.js": "9", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\layout.js": "10", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\todos.js": "11", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\messenger.js": "12", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\cards.js": "13", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\auth.js": "14", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\appointments.js": "15", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\users.js": "16", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\chats.js": "17", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\tasks.js": "18", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\styles\\vars.js": "19", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layouts.js": "20", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\cards.js": "21", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\todos.js": "22", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\messenger.js": "23", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\constants\\app.js": "24", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Caregivers.jsx": "25", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Tasks.jsx": "26", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AddCaregiver.jsx": "27", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AddUser.jsx": "28", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Login.jsx": "29", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\PageNotFound.jsx": "30", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Settings.jsx": "31", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Finances.jsx": "32", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DoctorsReviews.jsx": "33", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\PatientMessenger.jsx": "34", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DoctorMessenger.jsx": "35", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Staff.jsx": "36", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Tests.jsx": "37", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AppointmentDetails.jsx": "38", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\PatientReviews.jsx": "39", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DoctorAppointments.jsx": "40", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Appointments.jsx": "41", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardK.jsx": "42", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardJ.jsx": "43", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Nurses.jsx": "44", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardI.jsx": "45", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardH.jsx": "46", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardG.jsx": "47", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardF.jsx": "48", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardD.jsx": "49", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardE.jsx": "50", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardC.jsx": "51", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardB.jsx": "52", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardA.jsx": "53", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Dashboard.jsx": "54", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\config\\firebase.config.js": "55", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\ProtectedRoutes\\index.jsx": "56", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\PublicRoutes\\index.jsx": "57", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\storage.js": "58", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\constants\\options.js": "59", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\useNotistack.js": "60", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\service\\firebase.service.js": "61", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\UserSettings\\style.js": "62", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Widget\\style.js": "63", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Reviews\\Content.jsx": "64", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Reviews\\List.jsx": "65", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Page\\index.jsx": "66", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Widget\\index.jsx": "67", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\WidgetsLoader\\index.jsx": "68", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CaregiversList\\index.jsx": "69", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\TaskPlanner\\index.jsx": "70", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\DropFiles\\index.jsx": "71", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\UserSettings\\index.jsx": "72", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Widget\\WidgetBody\\index.jsx": "73", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\Balance\\index.jsx": "74", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CreditCards\\index.jsx": "75", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Select\\index.jsx": "76", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\MaskedInputs\\Date\\index.jsx": "77", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\TabNav\\index.jsx": "78", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\MaskedInputs\\Phone\\index.jsx": "79", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\TabNav\\TabNavItem\\index.jsx": "80", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\ModalWindow\\index.jsx": "81", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\index.jsx": "82", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PaymentsFeed\\index.jsx": "83", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Field\\index.jsx": "84", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\LabeledFormInput\\index.jsx": "85", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Btn\\index.jsx": "86", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\UsersList\\index.jsx": "87", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentListItem\\AppointmentListItem.jsx": "88", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsTests\\index.jsx": "89", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\StaffList\\index.jsx": "90", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\NoDataPlaceholder\\index.jsx": "91", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Avatar\\index.jsx": "92", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsList\\index.jsx": "93", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DoctorCalendar\\index.jsx": "94", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientCalendar\\index.jsx": "95", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\Statistics\\index.jsx": "96", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PaymentsHistory\\index.jsx": "97", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\RecentTests\\index.jsx": "98", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\AppointmentsScheduler\\index.jsx": "99", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PaymentsOverview\\index.jsx": "100", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DiseaseRate\\index.jsx": "101", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsRadialBar\\index.jsx": "102", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\RadarAreaChart\\index.jsx": "103", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\NursesList\\index.jsx": "104", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DailyPlanner\\index.jsx": "105", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\EpiContextPeriodChart\\index.jsx": "106", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\RecoveryRate\\index.jsx": "107", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\MapChart\\index.jsx": "108", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DailyAppointmentChart\\index.jsx": "109", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\EpiContextAreaChart\\index.jsx": "110", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\HealthIndexChart\\index.jsx": "111", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DoctorRatingList\\index.jsx": "112", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PainLocation\\index.jsx": "113", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DoctorCureRate\\index.jsx": "114", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\HepatitisChart\\index.jsx": "115", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\TaskScheduler\\index.jsx": "116", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsGenderLineChart\\index.jsx": "117", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\BloodTest\\index.jsx": "118", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\HeartRate\\index.jsx": "119", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\EventsCompactCalendar\\index.jsx": "120", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DiagnosesDonut\\index.jsx": "121", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DailyAppointmentsByDoc\\index.jsx": "122", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\usePageIsOverflow.js": "123", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\GenderScatter\\index.jsx": "124", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\useWindowSize.js": "125", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\RecentQuestions\\index.jsx": "126", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\TasksList\\index.jsx": "127", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\ConfirmedDiagnoses\\index.jsx": "128", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientOverallAppointments\\index.jsx": "129", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientAppointmentsHistory\\index.jsx": "130", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\NextPatient\\index.jsx": "131", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\UpcomingAppointments\\index.jsx": "132", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\LaboratoryTests\\index.jsx": "133", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DoctorOverallAppointment\\index.jsx": "134", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsPace\\index.jsx": "135", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Sidebar\\index.jsx": "136", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Panel\\index.jsx": "137", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\ScrollProgress\\index.jsx": "138", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\doctors.js": "139", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\config\\axios.config.js": "140", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Reviews\\style.js": "141", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\doctors_reviews.js": "142", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\useContentHeight.js": "143", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\dates.js": "144", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Reviews\\Review.jsx": "145", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Page\\style.js": "146", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\useGenderFilter.js": "147", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\useArrayNav.js": "148", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DailyPlanner\\style.js": "149", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Badge\\style.js": "150", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\GlobalSettingsControls\\style.js": "151", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\TextArea\\TextArea.jsx": "152", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Navigator\\index.jsx": "153", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\ScrollContainer\\index.jsx": "154", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\SelectPlaceholder\\index.jsx": "155", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\GlobalSettingsControls\\index.jsx": "156", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\CustomRating\\index.jsx": "157", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\DoctorRatingItem\\index.jsx": "158", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Widget\\WidgetHeader\\index.jsx": "159", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Grid\\index.jsx": "160", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CaregiversList\\Group\\index.jsx": "161", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\GenderNav\\index.jsx": "162", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\SearchBar\\index.jsx": "163", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\AssignShiftModal\\index.jsx": "164", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\UserSettings\\Form\\index.jsx": "165", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\PersonList\\Item\\index.jsx": "166", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\AddForm\\index.jsx": "167", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\TodosLegend\\index.jsx": "168", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\DnDLayout\\index.jsx": "169", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\Balance\\style.js": "170", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CreditCards\\style.js": "171", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Select\\style.js": "172", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\TabNav\\style.js": "173", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\style.js": "174", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\payments.js": "175", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PaymentsFeed\\style.js": "176", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\MaskedInputs\\Date\\style.js": "177", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentListItem\\style.js": "178", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsTests\\style.js": "179", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CreditCards\\Card\\index.jsx": "180", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CreditCards\\Form\\index.jsx": "181", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\Main\\index.jsx": "182", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\UsersList\\User\\index.jsx": "183", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PaymentsFeed\\Payments\\index.jsx": "184", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\patient_tests.js": "185", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\staff.js": "186", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentListItem\\AppointmentChip.jsx": "187", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentListItem\\AppointmentStatus.jsx": "188", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\constants\\framer.js": "189", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\Statistics\\style.js": "190", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsList\\style.js": "191", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\payments_history.js": "192", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\constants\\colors.js": "193", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PaymentsOverview\\style.js": "194", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\AppointmentsScheduler\\style.js": "195", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\MapChart\\style.js": "196", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\usePeriodNav.js": "197", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\disease.js": "198", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\numbers.js": "199", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\epi_period.js": "200", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\HealthIndexChart\\style.js": "201", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Reminder\\index.jsx": "202", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\ActionButton\\index.jsx": "203", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\ShapeButton\\index.jsx": "204", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\TestItem\\index.jsx": "205", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\GroupSeparator\\index.jsx": "206", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\health.js": "207", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\index.jsx": "208", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PainLocation\\style.js": "209", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\cure.js": "210", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\TaskScheduler\\style.js": "211", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Navigator\\MonthNavigator\\index.jsx": "212", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\StaffList\\Group\\index.jsx": "213", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsList\\Group\\index.jsx": "214", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\PaymentItem\\index.jsx": "215", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\DailyNavigation\\index.jsx": "216", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\scheduler.js": "217", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\Event\\index.jsx": "218", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\SelectableSlot\\index.jsx": "219", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Legend\\index.jsx": "220", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\LabeledProgress\\index.jsx": "221", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\PeriodNav\\index.jsx": "222", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\HeartRate\\style.js": "223", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\gender.js": "224", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\EventsCompactCalendar\\style.js": "225", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Legend\\LegendItem\\index.jsx": "226", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\scatter.js": "227", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\RecentQuestions\\style.js": "228", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\confirmed.js": "229", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\appointments.js": "230", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Widget\\WidgetNav\\index.jsx": "231", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\NursesList\\Group\\index.jsx": "232", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\CartesianChart\\index.jsx": "233", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Progress\\index.jsx": "234", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Timestamp\\index.jsx": "235", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PainLocation\\Body\\index.jsx": "236", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\ModalWindow\\CloseBtn\\index.jsx": "237", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DiagnosesDonut\\ChartLabel\\index.jsx": "238", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Chart\\Tooltip\\index.jsx": "239", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Chart\\Labels\\index.jsx": "240", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\OverallAppointmentChart\\ChartLegend\\index.jsx": "241", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\OverallAppointmentChart\\ChartBars\\index.jsx": "242", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\NextPatient\\style.js": "243", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\LaboratoryTests\\style.js": "244", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\upcoming.js": "245", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\UpcomingAppointments\\style.js": "246", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\RecentQuestions\\RecentQuestionsItem\\index.jsx": "247", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Truncated.jsx": "248", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\ConfirmedDiagnoses\\List\\index.jsx": "249", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentItem\\style.js": "250", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentItem\\index.jsx": "251", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\usePanelScroll.js": "252", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Panel\\style.js": "253", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Sidebar\\style.js": "254", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\MenuDots\\index.jsx": "255", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\IconLink\\index.jsx": "256", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Pill\\index.jsx": "257", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Sidebar\\Content\\index.jsx": "258", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Sidebar\\Emergency\\index.jsx": "259", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\MenuButton\\index.jsx": "260", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Logo\\index.jsx": "261", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Panel\\CurrentUser\\index.jsx": "262", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Navigator\\style.js": "263", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\DoctorRatingItem\\style.js": "264", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\useElementScroll.js": "265", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\AssignShiftModal\\style.js": "266", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\RangeSlider\\index.jsx": "267", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\PersonList\\index.jsx": "268", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\ActionButton\\ChatBtn.jsx": "269", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\PersonList\\Item\\style.js": "270", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\ActionButton\\TextBtn.jsx": "271", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\ActionButton\\EditBtn.jsx": "272", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\DnDLayout\\style.js": "273", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\AddForm\\style.js": "274", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\Todo\\index.jsx": "275", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\ConfirmationModal\\index.jsx": "276", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\styles\\keyframes.js": "277", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CreditCards\\Card\\style.js": "278", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CreditCards\\Form\\style.js": "279", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PaymentsFeed\\Payments\\style.js": "280", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\UsersList\\User\\style.js": "281", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\style.js": "282", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\TestItem\\style.js": "283", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\calendar_appointments.js": "284", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Radio\\index.jsx": "285", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\Message\\index.jsx": "286", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\Header\\index.jsx": "287", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\Input\\index.jsx": "288", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\TimeSlot\\index.jsx": "289", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\WeeklyNavigation\\index.jsx": "290", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\DailyToolbar\\index.jsx": "291", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\WeekSelector\\index.jsx": "292", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\MonthlyNavigation\\index.jsx": "293", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\MonthSelector\\index.jsx": "294", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\DoctorPopup\\index.jsx": "295", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\ScheduleAppointmentModal\\index.jsx": "296", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Legend\\style.js": "297", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\Event\\style.js": "298", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\PaymentItem\\style.js": "299", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\recharts.js": "300", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\OverallAppointmentChart\\ChartBars\\style.js": "301", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\CustomTooltip\\index.jsx": "302", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\ConfirmedDiagnoses\\List\\style.js": "303", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Sidebar\\Emergency\\style.js": "304", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\constants\\menu.js": "305", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\Todo\\planner.js": "306", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\Todo\\list.js": "307", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\Message\\style.js": "308", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\Header\\style.js": "309", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Checkbox\\index.jsx": "310", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\DoctorPopup\\style.js": "311", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Waveform\\index.jsx": "312", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\notifications.js": "313", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Notifications.jsx": "314", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AddPatient.jsx": "315", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\PatientDetails.jsx": "316", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Patients.jsx": "317", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AddPatientStep1.jsx": "318", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\NotificationItem\\index.jsx": "319", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AddPatientStep2.jsx": "320", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AddPatientStep4.jsx": "321", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AddPatientStep3.jsx": "322", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\CircularProgress\\index.jsx": "323", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\RecentPatients\\index.jsx": "324", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\TopStaff\\index.jsx": "325", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\StaffDetails.jsx": "326", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\appointmentValidation.js": "327", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentActionButtons\\index.jsx": "328", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\useScrollLock.js": "329", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\MobileAppointmentsList.jsx": "330", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\recurringAppointmentHelpers.js": "331", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\MobileCalendar\\index.jsx": "332", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\MobileCalendar\\style.js": "333", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\selectors\\chatSelectors.js": "334", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\phoneValidation.js": "335", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\PhoneNumberInput\\index.jsx": "336", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\VisitNotesSidebar\\index.jsx": "337", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\AddressInput\\index.jsx": "338", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AssignAdminModal\\index.jsx": "339"}, {"size": 493, "mtime": 1748930177455, "results": "340", "hashOfConfig": "341"}, {"size": 2423, "mtime": 1748930176893, "results": "342", "hashOfConfig": "341"}, {"size": 1042, "mtime": 1750422155861, "results": "343", "hashOfConfig": "341"}, {"size": 2878, "mtime": 1748930177406, "results": "344", "hashOfConfig": "341"}, {"size": 702, "mtime": 1748930177407, "results": "345", "hashOfConfig": "341"}, {"size": 6521, "mtime": 1753427179962, "results": "346", "hashOfConfig": "341"}, {"size": 9794, "mtime": 1750825468425, "results": "347", "hashOfConfig": "341"}, {"size": 2183, "mtime": 1748930177544, "results": "348", "hashOfConfig": "341"}, {"size": 1417, "mtime": 1752292100891, "results": "349", "hashOfConfig": "341"}, {"size": 805, "mtime": 1748930177530, "results": "350", "hashOfConfig": "341"}, {"size": 1721, "mtime": 1748930177533, "results": "351", "hashOfConfig": "341"}, {"size": 500, "mtime": 1748930177531, "results": "352", "hashOfConfig": "341"}, {"size": 1487, "mtime": 1748930177528, "results": "353", "hashOfConfig": "341"}, {"size": 1929, "mtime": 1753427289432, "results": "354", "hashOfConfig": "341"}, {"size": 6905, "mtime": 1751870641657, "results": "355", "hashOfConfig": "341"}, {"size": 11145, "mtime": 1753170691237, "results": "356", "hashOfConfig": "341"}, {"size": 7855, "mtime": 1751870641660, "results": "357", "hashOfConfig": "341"}, {"size": 4723, "mtime": 1749538416709, "results": "358", "hashOfConfig": "341"}, {"size": 2657, "mtime": 1751870641666, "results": "359", "hashOfConfig": "341"}, {"size": 9994, "mtime": 1749538416672, "results": "360", "hashOfConfig": "341"}, {"size": 928, "mtime": 1748930177411, "results": "361", "hashOfConfig": "341"}, {"size": 2363, "mtime": 1748930177433, "results": "362", "hashOfConfig": "341"}, {"size": 33263, "mtime": 1748930177424, "results": "363", "hashOfConfig": "341"}, {"size": 760, "mtime": 1751874477989, "results": "364", "hashOfConfig": "341"}, {"size": 2707, "mtime": 1748930177492, "results": "365", "hashOfConfig": "341"}, {"size": 448, "mtime": 1748930177521, "results": "366", "hashOfConfig": "341"}, {"size": 8122, "mtime": 1752292100868, "results": "367", "hashOfConfig": "341"}, {"size": 47118, "mtime": 1753150938914, "results": "368", "hashOfConfig": "341"}, {"size": 11170, "mtime": 1753421632209, "results": "369", "hashOfConfig": "341"}, {"size": 1560, "mtime": 1748930177515, "results": "370", "hashOfConfig": "341"}, {"size": 244, "mtime": 1753421632212, "results": "371", "hashOfConfig": "341"}, {"size": 581, "mtime": 1748930177512, "results": "372", "hashOfConfig": "341"}, {"size": 1690, "mtime": 1748930177511, "results": "373", "hashOfConfig": "341"}, {"size": 2387, "mtime": 1748930177517, "results": "374", "hashOfConfig": "341"}, {"size": 1657, "mtime": 1748930177510, "results": "375", "hashOfConfig": "341"}, {"size": 2527, "mtime": 1753175681225, "results": "376", "hashOfConfig": "341"}, {"size": 266, "mtime": 1748930177522, "results": "377", "hashOfConfig": "341"}, {"size": 17797, "mtime": 1752292100880, "results": "378", "hashOfConfig": "341"}, {"size": 1695, "mtime": 1748930177518, "results": "379", "hashOfConfig": "341"}, {"size": 796, "mtime": 1748930177509, "results": "380", "hashOfConfig": "341"}, {"size": 704, "mtime": 1749538416690, "results": "381", "hashOfConfig": "341"}, {"size": 1054, "mtime": 1748930177508, "results": "382", "hashOfConfig": "341"}, {"size": 945, "mtime": 1748930177506, "results": "383", "hashOfConfig": "341"}, {"size": 646, "mtime": 1748930177514, "results": "384", "hashOfConfig": "341"}, {"size": 820, "mtime": 1748930177505, "results": "385", "hashOfConfig": "341"}, {"size": 1011, "mtime": 1748930177504, "results": "386", "hashOfConfig": "341"}, {"size": 666, "mtime": 1748930177503, "results": "387", "hashOfConfig": "341"}, {"size": 818, "mtime": 1748930177502, "results": "388", "hashOfConfig": "341"}, {"size": 760, "mtime": 1748930177500, "results": "389", "hashOfConfig": "341"}, {"size": 782, "mtime": 1748930177501, "results": "390", "hashOfConfig": "341"}, {"size": 800, "mtime": 1748930177499, "results": "391", "hashOfConfig": "341"}, {"size": 811, "mtime": 1748930177497, "results": "392", "hashOfConfig": "341"}, {"size": 1407, "mtime": 1748930177497, "results": "393", "hashOfConfig": "341"}, {"size": 1753, "mtime": 1749708193025, "results": "394", "hashOfConfig": "341"}, {"size": 819, "mtime": 1749538416655, "results": "395", "hashOfConfig": "341"}, {"size": 1929, "mtime": 1753427247136, "results": "396", "hashOfConfig": "341"}, {"size": 669, "mtime": 1748930177472, "results": "397", "hashOfConfig": "341"}, {"size": 1732, "mtime": 1748930177548, "results": "398", "hashOfConfig": "341"}, {"size": 3504, "mtime": 1750422155813, "results": "399", "hashOfConfig": "341"}, {"size": 442, "mtime": 1748930177450, "results": "400", "hashOfConfig": "341"}, {"size": 733, "mtime": 1753170620798, "results": "401", "hashOfConfig": "341"}, {"size": 2203, "mtime": 1749538416743, "results": "402", "hashOfConfig": "341"}, {"size": 3239, "mtime": 1750858389295, "results": "403", "hashOfConfig": "341"}, {"size": 7587, "mtime": 1748930177361, "results": "404", "hashOfConfig": "341"}, {"size": 3079, "mtime": 1748930177362, "results": "405", "hashOfConfig": "341"}, {"size": 4360, "mtime": 1753160460319, "results": "406", "hashOfConfig": "341"}, {"size": 1000, "mtime": 1748930177393, "results": "407", "hashOfConfig": "341"}, {"size": 738, "mtime": 1748930177396, "results": "408", "hashOfConfig": "341"}, {"size": 3181, "mtime": 1749538416726, "results": "409", "hashOfConfig": "341"}, {"size": 3037, "mtime": 1749538416735, "results": "410", "hashOfConfig": "341"}, {"size": 1164, "mtime": 1748930177307, "results": "411", "hashOfConfig": "341"}, {"size": 1297, "mtime": 1748930177677, "results": "412", "hashOfConfig": "341"}, {"size": 535, "mtime": 1748930177389, "results": "413", "hashOfConfig": "341"}, {"size": 1140, "mtime": 1748930177553, "results": "414", "hashOfConfig": "341"}, {"size": 3636, "mtime": 1748930177571, "results": "415", "hashOfConfig": "341"}, {"size": 2082, "mtime": 1750422155679, "results": "416", "hashOfConfig": "341"}, {"size": 1945, "mtime": 1750830973898, "results": "417", "hashOfConfig": "341"}, {"size": 431, "mtime": 1748930176965, "results": "418", "hashOfConfig": "341"}, {"size": 537, "mtime": 1751874477986, "results": "419", "hashOfConfig": "341"}, {"size": 612, "mtime": 1748930176963, "results": "420", "hashOfConfig": "341"}, {"size": 2210, "mtime": 1751870641601, "results": "421", "hashOfConfig": "341"}, {"size": 680, "mtime": 1748930177336, "results": "422", "hashOfConfig": "341"}, {"size": 1781, "mtime": 1748930177644, "results": "423", "hashOfConfig": "341"}, {"size": 2211, "mtime": 1748930176919, "results": "424", "hashOfConfig": "341"}, {"size": 1049, "mtime": 1748930176925, "results": "425", "hashOfConfig": "341"}, {"size": 1922, "mtime": 1748930176907, "results": "426", "hashOfConfig": "341"}, {"size": 5942, "mtime": 1750858389295, "results": "427", "hashOfConfig": "341"}, {"size": 2513, "mtime": 1751870641548, "results": "428", "hashOfConfig": "341"}, {"size": 3236, "mtime": 1748930177638, "results": "429", "hashOfConfig": "341"}, {"size": 3128, "mtime": 1749538416734, "results": "430", "hashOfConfig": "341"}, {"size": 1756, "mtime": 1748930177344, "results": "431", "hashOfConfig": "341"}, {"size": 2169, "mtime": 1749538416604, "results": "432", "hashOfConfig": "341"}, {"size": 3719, "mtime": 1750422155883, "results": "433", "hashOfConfig": "341"}, {"size": 391, "mtime": 1748930177586, "results": "434", "hashOfConfig": "341"}, {"size": 386, "mtime": 1748930177626, "results": "435", "hashOfConfig": "341"}, {"size": 1440, "mtime": 1748930177662, "results": "436", "hashOfConfig": "341"}, {"size": 4206, "mtime": 1748930177646, "results": "437", "hashOfConfig": "341"}, {"size": 2640, "mtime": 1748930177656, "results": "438", "hashOfConfig": "341"}, {"size": 5797, "mtime": 1750758867143, "results": "439", "hashOfConfig": "341"}, {"size": 3297, "mtime": 1748930177648, "results": "440", "hashOfConfig": "341"}, {"size": 5418, "mtime": 1748930177584, "results": "441", "hashOfConfig": "341"}, {"size": 2907, "mtime": 1748930177636, "results": "442", "hashOfConfig": "341"}, {"size": 3242, "mtime": 1748930177651, "results": "443", "hashOfConfig": "341"}, {"size": 3459, "mtime": 1748930177619, "results": "444", "hashOfConfig": "341"}, {"size": 1955, "mtime": 1748930177578, "results": "445", "hashOfConfig": "341"}, {"size": 4039, "mtime": 1748930177596, "results": "446", "hashOfConfig": "341"}, {"size": 2936, "mtime": 1748930177658, "results": "447", "hashOfConfig": "341"}, {"size": 3773, "mtime": 1748930177613, "results": "448", "hashOfConfig": "341"}, {"size": 2366, "mtime": 1748930177574, "results": "449", "hashOfConfig": "341"}, {"size": 2064, "mtime": 1748930177593, "results": "450", "hashOfConfig": "341"}, {"size": 6581, "mtime": 1748930177602, "results": "451", "hashOfConfig": "341"}, {"size": 2484, "mtime": 1748930177591, "results": "452", "hashOfConfig": "341"}, {"size": 2026, "mtime": 1748930177622, "results": "453", "hashOfConfig": "341"}, {"size": 3334, "mtime": 1748930177588, "results": "454", "hashOfConfig": "341"}, {"size": 2161, "mtime": 1748930177609, "results": "455", "hashOfConfig": "341"}, {"size": 8065, "mtime": 1748930177667, "results": "456", "hashOfConfig": "341"}, {"size": 3457, "mtime": 1748930177629, "results": "457", "hashOfConfig": "341"}, {"size": 2007, "mtime": 1748930177556, "results": "458", "hashOfConfig": "341"}, {"size": 838, "mtime": 1748930177605, "results": "459", "hashOfConfig": "341"}, {"size": 3243, "mtime": 1748930177597, "results": "460", "hashOfConfig": "341"}, {"size": 4841, "mtime": 1748930177582, "results": "461", "hashOfConfig": "341"}, {"size": 4235, "mtime": 1748930177576, "results": "462", "hashOfConfig": "341"}, {"size": 661, "mtime": 1748930177452, "results": "463", "hashOfConfig": "341"}, {"size": 3749, "mtime": 1748930177601, "results": "464", "hashOfConfig": "341"}, {"size": 634, "mtime": 1748930177454, "results": "465", "hashOfConfig": "341"}, {"size": 4358, "mtime": 1750352348123, "results": "466", "hashOfConfig": "341"}, {"size": 2047, "mtime": 1748930177671, "results": "467", "hashOfConfig": "341"}, {"size": 1493, "mtime": 1748930177564, "results": "468", "hashOfConfig": "341"}, {"size": 2018, "mtime": 1749538416728, "results": "469", "hashOfConfig": "341"}, {"size": 2042, "mtime": 1748930177625, "results": "470", "hashOfConfig": "341"}, {"size": 4456, "mtime": 1748930177615, "results": "471", "hashOfConfig": "341"}, {"size": 5093, "mtime": 1750937862437, "results": "472", "hashOfConfig": "341"}, {"size": 3829, "mtime": 1748930177610, "results": "473", "hashOfConfig": "341"}, {"size": 2362, "mtime": 1748930177589, "results": "474", "hashOfConfig": "341"}, {"size": 4088, "mtime": 1748930177634, "results": "475", "hashOfConfig": "341"}, {"size": 2178, "mtime": 1751870641620, "results": "476", "hashOfConfig": "341"}, {"size": 4469, "mtime": 1751870641614, "results": "477", "hashOfConfig": "341"}, {"size": 1016, "mtime": 1748930176951, "results": "478", "hashOfConfig": "341"}, {"size": 5727, "mtime": 1748930177416, "results": "479", "hashOfConfig": "341"}, {"size": 1639, "mtime": 1753427266294, "results": "480", "hashOfConfig": "341"}, {"size": 3787, "mtime": 1748930177364, "results": "481", "hashOfConfig": "341"}, {"size": 73799, "mtime": 1748930177419, "results": "482", "hashOfConfig": "341"}, {"size": 551, "mtime": 1748930177447, "results": "483", "hashOfConfig": "341"}, {"size": 3727, "mtime": 1752292100894, "results": "484", "hashOfConfig": "341"}, {"size": 1543, "mtime": 1748930177363, "results": "485", "hashOfConfig": "341"}, {"size": 1255, "mtime": 1748930177464, "results": "486", "hashOfConfig": "341"}, {"size": 367, "mtime": 1748930177449, "results": "487", "hashOfConfig": "341"}, {"size": 562, "mtime": 1748930177446, "results": "488", "hashOfConfig": "341"}, {"size": 394, "mtime": 1748930177579, "results": "489", "hashOfConfig": "341"}, {"size": 1078, "mtime": 1748930176906, "results": "490", "hashOfConfig": "341"}, {"size": 1050, "mtime": 1748930177313, "results": "491", "hashOfConfig": "341"}, {"size": 1750, "mtime": 1749538416613, "results": "492", "hashOfConfig": "341"}, {"size": 958, "mtime": 1748930176940, "results": "493", "hashOfConfig": "341"}, {"size": 1439, "mtime": 1750858389295, "results": "494", "hashOfConfig": "341"}, {"size": 678, "mtime": 1748930176958, "results": "495", "hashOfConfig": "341"}, {"size": 3975, "mtime": 1748930177311, "results": "496", "hashOfConfig": "341"}, {"size": 941, "mtime": 1748930176914, "results": "497", "hashOfConfig": "341"}, {"size": 1120, "mtime": 1748930177304, "results": "498", "hashOfConfig": "341"}, {"size": 752, "mtime": 1748930177390, "results": "499", "hashOfConfig": "341"}, {"size": 1777, "mtime": 1750858389341, "results": "500", "hashOfConfig": "341"}, {"size": 890, "mtime": 1748930177558, "results": "501", "hashOfConfig": "341"}, {"size": 1125, "mtime": 1748930177309, "results": "502", "hashOfConfig": "341"}, {"size": 1917, "mtime": 1748930176953, "results": "503", "hashOfConfig": "341"}, {"size": 43071, "mtime": 1751870641557, "results": "504", "hashOfConfig": "341"}, {"size": 4488, "mtime": 1752292100901, "results": "505", "hashOfConfig": "341"}, {"size": 9175, "mtime": 1750422155735, "results": "506", "hashOfConfig": "341"}, {"size": 2965, "mtime": 1749538416653, "results": "507", "hashOfConfig": "341"}, {"size": 335, "mtime": 1748930177384, "results": "508", "hashOfConfig": "341"}, {"size": 2762, "mtime": 1750352366334, "results": "509", "hashOfConfig": "341"}, {"size": 836, "mtime": 1748930177554, "results": "510", "hashOfConfig": "341"}, {"size": 415, "mtime": 1748930177572, "results": "511", "hashOfConfig": "341"}, {"size": 3616, "mtime": 1750422155680, "results": "512", "hashOfConfig": "341"}, {"size": 1183, "mtime": 1748930176966, "results": "513", "hashOfConfig": "341"}, {"size": 1428, "mtime": 1750858389295, "results": "514", "hashOfConfig": "341"}, {"size": 2129, "mtime": 1748930177427, "results": "515", "hashOfConfig": "341"}, {"size": 715, "mtime": 1748930177645, "results": "516", "hashOfConfig": "341"}, {"size": 644, "mtime": 1750830827518, "results": "517", "hashOfConfig": "341"}, {"size": 3083, "mtime": 1748930177262, "results": "518", "hashOfConfig": "341"}, {"size": 881, "mtime": 1748930177639, "results": "519", "hashOfConfig": "341"}, {"size": 2934, "mtime": 1748930177566, "results": "520", "hashOfConfig": "341"}, {"size": 3108, "mtime": 1748930177569, "results": "521", "hashOfConfig": "341"}, {"size": 4272, "mtime": 1750858389356, "results": "522", "hashOfConfig": "341"}, {"size": 4992, "mtime": 1751870641593, "results": "523", "hashOfConfig": "341"}, {"size": 3417, "mtime": 1748930177642, "results": "524", "hashOfConfig": "341"}, {"size": 4796, "mtime": 1748930177425, "results": "525", "hashOfConfig": "341"}, {"size": 5166, "mtime": 1748930177432, "results": "526", "hashOfConfig": "341"}, {"size": 863, "mtime": 1748930177258, "results": "527", "hashOfConfig": "341"}, {"size": 1319, "mtime": 1751870641551, "results": "528", "hashOfConfig": "341"}, {"size": 161, "mtime": 1748930177402, "results": "529", "hashOfConfig": "341"}, {"size": 864, "mtime": 1748930177663, "results": "530", "hashOfConfig": "341"}, {"size": 1840, "mtime": 1748930177633, "results": "531", "hashOfConfig": "341"}, {"size": 2563, "mtime": 1748930177428, "results": "532", "hashOfConfig": "341"}, {"size": 526, "mtime": 1750828741460, "results": "533", "hashOfConfig": "341"}, {"size": 922, "mtime": 1748930177649, "results": "534", "hashOfConfig": "341"}, {"size": 2713, "mtime": 1750422155879, "results": "535", "hashOfConfig": "341"}, {"size": 717, "mtime": 1748930177613, "results": "536", "hashOfConfig": "341"}, {"size": 327, "mtime": 1748930177453, "results": "537", "hashOfConfig": "341"}, {"size": 1159, "mtime": 1748930177414, "results": "538", "hashOfConfig": "341"}, {"size": 341, "mtime": 1748930177545, "results": "539", "hashOfConfig": "341"}, {"size": 2738, "mtime": 1748930177420, "results": "540", "hashOfConfig": "341"}, {"size": 1400, "mtime": 1748930177604, "results": "541", "hashOfConfig": "341"}, {"size": 1079, "mtime": 1748930176950, "results": "542", "hashOfConfig": "341"}, {"size": 1392, "mtime": 1748930176901, "results": "543", "hashOfConfig": "341"}, {"size": 3065, "mtime": 1750649101227, "results": "544", "hashOfConfig": "341"}, {"size": 1074, "mtime": 1748930177370, "results": "545", "hashOfConfig": "341"}, {"size": 1215, "mtime": 1748930176921, "results": "546", "hashOfConfig": "341"}, {"size": 1707, "mtime": 1748930177422, "results": "547", "hashOfConfig": "341"}, {"size": 20594, "mtime": 1751870641581, "results": "548", "hashOfConfig": "341"}, {"size": 3715, "mtime": 1748930177623, "results": "549", "hashOfConfig": "341"}, {"size": 3715, "mtime": 1748930177413, "results": "550", "hashOfConfig": "341"}, {"size": 4296, "mtime": 1748930177668, "results": "551", "hashOfConfig": "341"}, {"size": 1763, "mtime": 1748930176939, "results": "552", "hashOfConfig": "341"}, {"size": 890, "mtime": 1748930177660, "results": "553", "hashOfConfig": "341"}, {"size": 739, "mtime": 1748930177631, "results": "554", "hashOfConfig": "341"}, {"size": 1143, "mtime": 1748930177351, "results": "555", "hashOfConfig": "341"}, {"size": 1281, "mtime": 1750422155693, "results": "556", "hashOfConfig": "341"}, {"size": 3094, "mtime": 1748930177431, "results": "557", "hashOfConfig": "341"}, {"size": 13468, "mtime": 1751870641567, "results": "558", "hashOfConfig": "341"}, {"size": 670, "mtime": 1750422155713, "results": "559", "hashOfConfig": "341"}, {"size": 388, "mtime": 1748930176930, "results": "560", "hashOfConfig": "341"}, {"size": 844, "mtime": 1748930176927, "results": "561", "hashOfConfig": "341"}, {"size": 635, "mtime": 1748930177354, "results": "562", "hashOfConfig": "341"}, {"size": 1810, "mtime": 1748930177607, "results": "563", "hashOfConfig": "341"}, {"size": 3915, "mtime": 1748930177421, "results": "564", "hashOfConfig": "341"}, {"size": 3764, "mtime": 1748930177599, "results": "565", "hashOfConfig": "341"}, {"size": 446, "mtime": 1748930176929, "results": "566", "hashOfConfig": "341"}, {"size": 1645, "mtime": 1748930177429, "results": "567", "hashOfConfig": "341"}, {"size": 757, "mtime": 1748930177655, "results": "568", "hashOfConfig": "341"}, {"size": 1715, "mtime": 1748930177412, "results": "569", "hashOfConfig": "341"}, {"size": 4695, "mtime": 1748930177408, "results": "570", "hashOfConfig": "341"}, {"size": 1048, "mtime": 1748930177392, "results": "571", "hashOfConfig": "341"}, {"size": 890, "mtime": 1748930177618, "results": "572", "hashOfConfig": "341"}, {"size": 6540, "mtime": 1748930177295, "results": "573", "hashOfConfig": "341"}, {"size": 1201, "mtime": 1748930176945, "results": "574", "hashOfConfig": "341"}, {"size": 646, "mtime": 1748930176969, "results": "575", "hashOfConfig": "341"}, {"size": 4282, "mtime": 1748930177621, "results": "576", "hashOfConfig": "341"}, {"size": 909, "mtime": 1750914251771, "results": "577", "hashOfConfig": "341"}, {"size": 1564, "mtime": 1748930177581, "results": "578", "hashOfConfig": "341"}, {"size": 1616, "mtime": 1748930177300, "results": "579", "hashOfConfig": "341"}, {"size": 276, "mtime": 1748930177298, "results": "580", "hashOfConfig": "341"}, {"size": 596, "mtime": 1748930177349, "results": "581", "hashOfConfig": "341"}, {"size": 2527, "mtime": 1748930177347, "results": "582", "hashOfConfig": "341"}, {"size": 217, "mtime": 1748930177616, "results": "583", "hashOfConfig": "341"}, {"size": 509, "mtime": 1748930177611, "results": "584", "hashOfConfig": "341"}, {"size": 6488, "mtime": 1748930177434, "results": "585", "hashOfConfig": "586"}, {"size": 1670, "mtime": 1748930177674, "results": "587", "hashOfConfig": "341"}, {"size": 1122, "mtime": 1748930177652, "results": "588", "hashOfConfig": "341"}, {"size": 612, "mtime": 1748930177385, "results": "589", "hashOfConfig": "341"}, {"size": 1588, "mtime": 1748930177562, "results": "590", "hashOfConfig": "341"}, {"size": 1387, "mtime": 1749554397518, "results": "591", "hashOfConfig": "341"}, {"size": 3542, "mtime": 1750422155687, "results": "592", "hashOfConfig": "341"}, {"size": 702, "mtime": 1748930177452, "results": "593", "hashOfConfig": "341"}, {"size": 4392, "mtime": 1750422155820, "results": "594", "hashOfConfig": "341"}, {"size": 1651, "mtime": 1751870641623, "results": "595", "hashOfConfig": "341"}, {"size": 773, "mtime": 1748930176937, "results": "596", "hashOfConfig": "341"}, {"size": 997, "mtime": 1748930176923, "results": "597", "hashOfConfig": "341"}, {"size": 1365, "mtime": 1748930176943, "results": "598", "hashOfConfig": "341"}, {"size": 3965, "mtime": 1751870641617, "results": "599", "hashOfConfig": "341"}, {"size": 531, "mtime": 1748930177476, "results": "600", "hashOfConfig": "341"}, {"size": 1128, "mtime": 1748930176935, "results": "601", "hashOfConfig": "341"}, {"size": 1266, "mtime": 1748930176933, "results": "602", "hashOfConfig": "341"}, {"size": 4334, "mtime": 1751870641609, "results": "603", "hashOfConfig": "341"}, {"size": 645, "mtime": 1748930176941, "results": "604", "hashOfConfig": "341"}, {"size": 1878, "mtime": 1748930177305, "results": "605", "hashOfConfig": "341"}, {"size": 1676, "mtime": 1748930177448, "results": "606", "hashOfConfig": "341"}, {"size": 3122, "mtime": 1751870641560, "results": "607", "hashOfConfig": "341"}, {"size": 801, "mtime": 1748930176948, "results": "608", "hashOfConfig": "341"}, {"size": 771, "mtime": 1749818406264, "results": "609", "hashOfConfig": "341"}, {"size": 1215, "mtime": 1748930176897, "results": "610", "hashOfConfig": "341"}, {"size": 3080, "mtime": 1749538416650, "results": "611", "hashOfConfig": "341"}, {"size": 1391, "mtime": 1749538416600, "results": "612", "hashOfConfig": "341"}, {"size": 1284, "mtime": 1749538416599, "results": "613", "hashOfConfig": "341"}, {"size": 904, "mtime": 1748930177378, "results": "614", "hashOfConfig": "341"}, {"size": 1058, "mtime": 1748930177375, "results": "615", "hashOfConfig": "341"}, {"size": 2633, "mtime": 1748930177380, "results": "616", "hashOfConfig": "341"}, {"size": 2276, "mtime": 1750422155725, "results": "617", "hashOfConfig": "341"}, {"size": 1235, "mtime": 1748930177538, "results": "618", "hashOfConfig": "341"}, {"size": 1144, "mtime": 1748930177567, "results": "619", "hashOfConfig": "341"}, {"size": 2169, "mtime": 1748930177570, "results": "620", "hashOfConfig": "341"}, {"size": 1508, "mtime": 1748930177643, "results": "621", "hashOfConfig": "341"}, {"size": 2388, "mtime": 1751870641597, "results": "622", "hashOfConfig": "341"}, {"size": 13174, "mtime": 1751870641585, "results": "623", "hashOfConfig": "341"}, {"size": 1486, "mtime": 1748930177371, "results": "624", "hashOfConfig": "341"}, {"size": 6977, "mtime": 1748930177410, "results": "625", "hashOfConfig": "341"}, {"size": 1186, "mtime": 1748930176946, "results": "626", "hashOfConfig": "341"}, {"size": 1152, "mtime": 1748930177328, "results": "627", "hashOfConfig": "341"}, {"size": 1242, "mtime": 1748930177321, "results": "628", "hashOfConfig": "341"}, {"size": 8075, "mtime": 1753364067887, "results": "629", "hashOfConfig": "341"}, {"size": 1418, "mtime": 1750758747675, "results": "630", "hashOfConfig": "341"}, {"size": 1113, "mtime": 1750422155719, "results": "631", "hashOfConfig": "341"}, {"size": 146, "mtime": 1748930177270, "results": "632", "hashOfConfig": "341"}, {"size": 788, "mtime": 1748930177287, "results": "633", "hashOfConfig": "341"}, {"size": 1394, "mtime": 1750422155711, "results": "634", "hashOfConfig": "341"}, {"size": 807, "mtime": 1748930177279, "results": "635", "hashOfConfig": "341"}, {"size": 9064, "mtime": 1750758922613, "results": "636", "hashOfConfig": "341"}, {"size": 21064, "mtime": 1751870641606, "results": "637", "hashOfConfig": "341"}, {"size": 456, "mtime": 1748930176931, "results": "638", "hashOfConfig": "341"}, {"size": 23470, "mtime": 1751870641572, "results": "639", "hashOfConfig": "341"}, {"size": 1137, "mtime": 1748930177352, "results": "640", "hashOfConfig": "341"}, {"size": 286, "mtime": 1748930177546, "results": "641", "hashOfConfig": "341"}, {"size": 1219, "mtime": 1748930177348, "results": "642", "hashOfConfig": "341"}, {"size": 1734, "mtime": 1748930176916, "results": "643", "hashOfConfig": "341"}, {"size": 482, "mtime": 1748930177563, "results": "644", "hashOfConfig": "341"}, {"size": 849, "mtime": 1748930177478, "results": "645", "hashOfConfig": "341"}, {"size": 3743, "mtime": 1750665092670, "results": "646", "hashOfConfig": "341"}, {"size": 687, "mtime": 1748930177382, "results": "647", "hashOfConfig": "341"}, {"size": 2312, "mtime": 1748930177381, "results": "648", "hashOfConfig": "341"}, {"size": 1343, "mtime": 1748930177330, "results": "649", "hashOfConfig": "341"}, {"size": 1028, "mtime": 1748930177322, "results": "650", "hashOfConfig": "341"}, {"size": 2129, "mtime": 1748930176909, "results": "651", "hashOfConfig": "341"}, {"size": 3059, "mtime": 1750914808372, "results": "652", "hashOfConfig": "341"}, {"size": 2941, "mtime": 1748930177386, "results": "653", "hashOfConfig": "341"}, {"size": 11535, "mtime": 1752292100888, "results": "654", "hashOfConfig": "341"}, {"size": 10067, "mtime": 1752292100884, "results": "655", "hashOfConfig": "341"}, {"size": 8082, "mtime": 1751874477992, "results": "656", "hashOfConfig": "341"}, {"size": 34682, "mtime": 1751870641646, "results": "657", "hashOfConfig": "341"}, {"size": 570, "mtime": 1749538416702, "results": "658", "hashOfConfig": "341"}, {"size": 29715, "mtime": 1753150842022, "results": "659", "hashOfConfig": "341"}, {"size": 2875, "mtime": 1751870641539, "results": "660", "hashOfConfig": "341"}, {"size": 20631, "mtime": 1749538416678, "results": "661", "hashOfConfig": "341"}, {"size": 21446, "mtime": 1750831400553, "results": "662", "hashOfConfig": "341"}, {"size": 18808, "mtime": 1749538416681, "results": "663", "hashOfConfig": "341"}, {"size": 1822, "mtime": 1749538416606, "results": "664", "hashOfConfig": "341"}, {"size": 9080, "mtime": 1750937924273, "results": "665", "hashOfConfig": "341"}, {"size": 5820, "mtime": 1750422155886, "results": "666", "hashOfConfig": "341"}, {"size": 18080, "mtime": 1751870641650, "results": "667", "hashOfConfig": "341"}, {"size": 24836, "mtime": 1751870641670, "results": "668", "hashOfConfig": "341"}, {"size": 9861, "mtime": 1752292100861, "results": "669", "hashOfConfig": "341"}, {"size": 1134, "mtime": 1749708193017, "results": "670", "hashOfConfig": "341"}, {"size": 5986, "mtime": 1751870641576, "results": "671", "hashOfConfig": "341"}, {"size": 5813, "mtime": 1751870641685, "results": "672", "hashOfConfig": "341"}, {"size": 4417, "mtime": 1750422155708, "results": "673", "hashOfConfig": "341"}, {"size": 4944, "mtime": 1750422155709, "results": "674", "hashOfConfig": "341"}, {"size": 492, "mtime": 1751870641653, "results": "675", "hashOfConfig": "341"}, {"size": 3378, "mtime": 1752292100897, "results": "676", "hashOfConfig": "341"}, {"size": 5091, "mtime": 1752292100846, "results": "677", "hashOfConfig": "341"}, {"size": 13313, "mtime": 1752292100864, "results": "678", "hashOfConfig": "341"}, {"size": 14214, "mtime": 1753150280763, "results": "679", "hashOfConfig": "341"}, {"size": 17514, "mtime": 1753175865074, "results": "680", "hashOfConfig": "341"}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1xtwf30", {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "687", "messages": "688", "suppressedMessages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "690", "messages": "691", "suppressedMessages": "692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "693", "messages": "694", "suppressedMessages": "695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "696", "messages": "697", "suppressedMessages": "698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "699", "messages": "700", "suppressedMessages": "701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "702", "messages": "703", "suppressedMessages": "704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "705", "messages": "706", "suppressedMessages": "707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "708", "messages": "709", "suppressedMessages": "710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "711", "messages": "712", "suppressedMessages": "713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "714", "messages": "715", "suppressedMessages": "716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "717", "messages": "718", "suppressedMessages": "719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "720", "messages": "721", "suppressedMessages": "722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "723", "messages": "724", "suppressedMessages": "725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "726", "messages": "727", "suppressedMessages": "728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "729", "messages": "730", "suppressedMessages": "731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "732", "messages": "733", "suppressedMessages": "734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "735", "messages": "736", "suppressedMessages": "737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "738", "messages": "739", "suppressedMessages": "740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "741", "messages": "742", "suppressedMessages": "743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "744", "messages": "745", "suppressedMessages": "746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "747", "messages": "748", "suppressedMessages": "749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "750", "messages": "751", "suppressedMessages": "752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "753", "messages": "754", "suppressedMessages": "755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "756", "messages": "757", "suppressedMessages": "758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "759", "messages": "760", "suppressedMessages": "761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "762", "messages": "763", "suppressedMessages": "764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "765", "messages": "766", "suppressedMessages": "767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "768", "messages": "769", "suppressedMessages": "770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "771", "messages": "772", "suppressedMessages": "773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "774", "messages": "775", "suppressedMessages": "776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "777", "messages": "778", "suppressedMessages": "779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "780", "messages": "781", "suppressedMessages": "782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "783", "messages": "784", "suppressedMessages": "785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "786", "messages": "787", "suppressedMessages": "788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "789", "messages": "790", "suppressedMessages": "791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "792", "messages": "793", "suppressedMessages": "794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "795", "messages": "796", "suppressedMessages": "797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "798", "messages": "799", "suppressedMessages": "800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "801", "messages": "802", "suppressedMessages": "803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "804", "messages": "805", "suppressedMessages": "806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "807", "messages": "808", "suppressedMessages": "809", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "810", "messages": "811", "suppressedMessages": "812", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "813", "messages": "814", "suppressedMessages": "815", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "816", "messages": "817", "suppressedMessages": "818", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "819", "messages": "820", "suppressedMessages": "821", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "822", "messages": "823", "suppressedMessages": "824", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "825", "messages": "826", "suppressedMessages": "827", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "828", "messages": "829", "suppressedMessages": "830", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "831", "messages": "832", "suppressedMessages": "833", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "834", "messages": "835", "suppressedMessages": "836", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "837", "messages": "838", "suppressedMessages": "839", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "840", "messages": "841", "suppressedMessages": "842", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "843", "messages": "844", "suppressedMessages": "845", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "846", "messages": "847", "suppressedMessages": "848", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "849", "messages": "850", "suppressedMessages": "851", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "852", "messages": "853", "suppressedMessages": "854", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "855", "messages": "856", "suppressedMessages": "857", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "858", "messages": "859", "suppressedMessages": "860", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "861", "messages": "862", "suppressedMessages": "863", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "864", "messages": "865", "suppressedMessages": "866", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "867", "messages": "868", "suppressedMessages": "869", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "870", "messages": "871", "suppressedMessages": "872", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "873", "messages": "874", "suppressedMessages": "875", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "876", "messages": "877", "suppressedMessages": "878", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "879", "messages": "880", "suppressedMessages": "881", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "882", "messages": "883", "suppressedMessages": "884", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "885", "messages": "886", "suppressedMessages": "887", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "888", "messages": "889", "suppressedMessages": "890", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "891", "messages": "892", "suppressedMessages": "893", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "894", "messages": "895", "suppressedMessages": "896", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "897", "messages": "898", "suppressedMessages": "899", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "900", "messages": "901", "suppressedMessages": "902", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "903", "messages": "904", "suppressedMessages": "905", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "906", "messages": "907", "suppressedMessages": "908", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "909", "messages": "910", "suppressedMessages": "911", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "912", "messages": "913", "suppressedMessages": "914", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "915", "messages": "916", "suppressedMessages": "917", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "918", "messages": "919", "suppressedMessages": "920", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "921", "messages": "922", "suppressedMessages": "923", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "924", "messages": "925", "suppressedMessages": "926", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "927", "messages": "928", "suppressedMessages": "929", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "930", "messages": "931", "suppressedMessages": "932", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "933", "messages": "934", "suppressedMessages": "935", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "936", "messages": "937", "suppressedMessages": "938", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "939", "messages": "940", "suppressedMessages": "941", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "942", "messages": "943", "suppressedMessages": "944", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "945", "messages": "946", "suppressedMessages": "947", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "948", "messages": "949", "suppressedMessages": "950", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "951", "messages": "952", "suppressedMessages": "953", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "954", "messages": "955", "suppressedMessages": "956", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "957", "messages": "958", "suppressedMessages": "959", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "960", "messages": "961", "suppressedMessages": "962", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "963", "messages": "964", "suppressedMessages": "965", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "966", "messages": "967", "suppressedMessages": "968", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "969", "messages": "970", "suppressedMessages": "971", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "972", "messages": "973", "suppressedMessages": "974", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "975", "messages": "976", "suppressedMessages": "977", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "978", "messages": "979", "suppressedMessages": "980", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "981", "messages": "982", "suppressedMessages": "983", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "984", "messages": "985", "suppressedMessages": "986", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "987", "messages": "988", "suppressedMessages": "989", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "990", "messages": "991", "suppressedMessages": "992", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "993", "messages": "994", "suppressedMessages": "995", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "996", "messages": "997", "suppressedMessages": "998", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "999", "messages": "1000", "suppressedMessages": "1001", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1002", "messages": "1003", "suppressedMessages": "1004", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1005", "messages": "1006", "suppressedMessages": "1007", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1008", "messages": "1009", "suppressedMessages": "1010", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1011", "messages": "1012", "suppressedMessages": "1013", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1014", "messages": "1015", "suppressedMessages": "1016", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1017", "messages": "1018", "suppressedMessages": "1019", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1020", "messages": "1021", "suppressedMessages": "1022", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1023", "messages": "1024", "suppressedMessages": "1025", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1026", "messages": "1027", "suppressedMessages": "1028", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1029", "messages": "1030", "suppressedMessages": "1031", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1032", "messages": "1033", "suppressedMessages": "1034", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1035", "messages": "1036", "suppressedMessages": "1037", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1038", "messages": "1039", "suppressedMessages": "1040", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1041", "messages": "1042", "suppressedMessages": "1043", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1044", "messages": "1045", "suppressedMessages": "1046", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1047", "messages": "1048", "suppressedMessages": "1049", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1050", "messages": "1051", "suppressedMessages": "1052", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1053", "messages": "1054", "suppressedMessages": "1055", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1056", "messages": "1057", "suppressedMessages": "1058", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1059", "messages": "1060", "suppressedMessages": "1061", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1062", "messages": "1063", "suppressedMessages": "1064", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1065", "messages": "1066", "suppressedMessages": "1067", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1068", "messages": "1069", "suppressedMessages": "1070", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1071", "messages": "1072", "suppressedMessages": "1073", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1074", "messages": "1075", "suppressedMessages": "1076", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1077", "messages": "1078", "suppressedMessages": "1079", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1080", "messages": "1081", "suppressedMessages": "1082", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1083", "messages": "1084", "suppressedMessages": "1085", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1086", "messages": "1087", "suppressedMessages": "1088", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1089", "messages": "1090", "suppressedMessages": "1091", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1092", "messages": "1093", "suppressedMessages": "1094", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1095", "messages": "1096", "suppressedMessages": "1097", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1098", "messages": "1099", "suppressedMessages": "1100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1101", "messages": "1102", "suppressedMessages": "1103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1104", "messages": "1105", "suppressedMessages": "1106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1107", "messages": "1108", "suppressedMessages": "1109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1110", "messages": "1111", "suppressedMessages": "1112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1113", "messages": "1114", "suppressedMessages": "1115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1116", "messages": "1117", "suppressedMessages": "1118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1119", "messages": "1120", "suppressedMessages": "1121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1122", "messages": "1123", "suppressedMessages": "1124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1125", "messages": "1126", "suppressedMessages": "1127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1128", "messages": "1129", "suppressedMessages": "1130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1131", "messages": "1132", "suppressedMessages": "1133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1134", "messages": "1135", "suppressedMessages": "1136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1137", "messages": "1138", "suppressedMessages": "1139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1140", "messages": "1141", "suppressedMessages": "1142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1143", "messages": "1144", "suppressedMessages": "1145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1146", "messages": "1147", "suppressedMessages": "1148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1149", "messages": "1150", "suppressedMessages": "1151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1152", "messages": "1153", "suppressedMessages": "1154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1155", "messages": "1156", "suppressedMessages": "1157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1158", "messages": "1159", "suppressedMessages": "1160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1161", "messages": "1162", "suppressedMessages": "1163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1164", "messages": "1165", "suppressedMessages": "1166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1167", "messages": "1168", "suppressedMessages": "1169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1170", "messages": "1171", "suppressedMessages": "1172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1173", "messages": "1174", "suppressedMessages": "1175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1176", "messages": "1177", "suppressedMessages": "1178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1179", "messages": "1180", "suppressedMessages": "1181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1182", "messages": "1183", "suppressedMessages": "1184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1185", "messages": "1186", "suppressedMessages": "1187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1188", "messages": "1189", "suppressedMessages": "1190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1191", "messages": "1192", "suppressedMessages": "1193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1194", "messages": "1195", "suppressedMessages": "1196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1197", "messages": "1198", "suppressedMessages": "1199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1200", "messages": "1201", "suppressedMessages": "1202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1203", "messages": "1204", "suppressedMessages": "1205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1206", "messages": "1207", "suppressedMessages": "1208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1209", "messages": "1210", "suppressedMessages": "1211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1212", "messages": "1213", "suppressedMessages": "1214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1215", "messages": "1216", "suppressedMessages": "1217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1218", "messages": "1219", "suppressedMessages": "1220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1221", "messages": "1222", "suppressedMessages": "1223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1224", "messages": "1225", "suppressedMessages": "1226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1227", "messages": "1228", "suppressedMessages": "1229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1230", "messages": "1231", "suppressedMessages": "1232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1233", "messages": "1234", "suppressedMessages": "1235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1236", "messages": "1237", "suppressedMessages": "1238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1239", "messages": "1240", "suppressedMessages": "1241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1242", "messages": "1243", "suppressedMessages": "1244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1245", "messages": "1246", "suppressedMessages": "1247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1248", "messages": "1249", "suppressedMessages": "1250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1251", "messages": "1252", "suppressedMessages": "1253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1254", "messages": "1255", "suppressedMessages": "1256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1257", "messages": "1258", "suppressedMessages": "1259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1260", "messages": "1261", "suppressedMessages": "1262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1263", "messages": "1264", "suppressedMessages": "1265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1266", "messages": "1267", "suppressedMessages": "1268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1269", "messages": "1270", "suppressedMessages": "1271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1272", "messages": "1273", "suppressedMessages": "1274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1275", "messages": "1276", "suppressedMessages": "1277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1278", "messages": "1279", "suppressedMessages": "1280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1281", "messages": "1282", "suppressedMessages": "1283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1284", "messages": "1285", "suppressedMessages": "1286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1287", "messages": "1288", "suppressedMessages": "1289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1290", "messages": "1291", "suppressedMessages": "1292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1293", "messages": "1294", "suppressedMessages": "1295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1296", "messages": "1297", "suppressedMessages": "1298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1299", "messages": "1300", "suppressedMessages": "1301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1302", "messages": "1303", "suppressedMessages": "1304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1305", "messages": "1306", "suppressedMessages": "1307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1308", "messages": "1309", "suppressedMessages": "1310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1311", "messages": "1312", "suppressedMessages": "1313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1314", "messages": "1315", "suppressedMessages": "1316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1317", "messages": "1318", "suppressedMessages": "1319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1320", "messages": "1321", "suppressedMessages": "1322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1323", "messages": "1324", "suppressedMessages": "1325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1326", "messages": "1327", "suppressedMessages": "1328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1329", "messages": "1330", "suppressedMessages": "1331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1332", "messages": "1333", "suppressedMessages": "1334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1335", "messages": "1336", "suppressedMessages": "1337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1338", "messages": "1339", "suppressedMessages": "1340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1341", "messages": "1342", "suppressedMessages": "1343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1344", "messages": "1345", "suppressedMessages": "1346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1347", "messages": "1348", "suppressedMessages": "1349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1350", "messages": "1351", "suppressedMessages": "1352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1353", "messages": "1354", "suppressedMessages": "1355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1356", "messages": "1357", "suppressedMessages": "1358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1359", "messages": "1360", "suppressedMessages": "1361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1362", "messages": "1363", "suppressedMessages": "1364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1365", "messages": "1366", "suppressedMessages": "1367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1368", "messages": "1369", "suppressedMessages": "1370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1371", "messages": "1372", "suppressedMessages": "1373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1374", "messages": "1375", "suppressedMessages": "1376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1377", "messages": "1378", "suppressedMessages": "1379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1380", "messages": "1381", "suppressedMessages": "1382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1383", "messages": "1384", "suppressedMessages": "1385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1386", "messages": "1387", "suppressedMessages": "1388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1389", "messages": "1390", "suppressedMessages": "1391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1392", "messages": "1393", "suppressedMessages": "1394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1395", "messages": "1396", "suppressedMessages": "1397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1398", "messages": "1399", "suppressedMessages": "1400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1401", "messages": "1402", "suppressedMessages": "1403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1404", "messages": "1405", "suppressedMessages": "1406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1407", "messages": "1408", "suppressedMessages": "1409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1410", "messages": "1411", "suppressedMessages": "1412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1413", "messages": "1414", "suppressedMessages": "1415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1gxv70p", {"filePath": "1416", "messages": "1417", "suppressedMessages": "1418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1419", "messages": "1420", "suppressedMessages": "1421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1422", "messages": "1423", "suppressedMessages": "1424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1425", "messages": "1426", "suppressedMessages": "1427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1428", "messages": "1429", "suppressedMessages": "1430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1431", "messages": "1432", "suppressedMessages": "1433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1434", "messages": "1435", "suppressedMessages": "1436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1437", "messages": "1438", "suppressedMessages": "1439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1440", "messages": "1441", "suppressedMessages": "1442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1443", "messages": "1444", "suppressedMessages": "1445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1446", "messages": "1447", "suppressedMessages": "1448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1449", "messages": "1450", "suppressedMessages": "1451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1452", "messages": "1453", "suppressedMessages": "1454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1455", "messages": "1456", "suppressedMessages": "1457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1458", "messages": "1459", "suppressedMessages": "1460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1461", "messages": "1462", "suppressedMessages": "1463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1464", "messages": "1465", "suppressedMessages": "1466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1467", "messages": "1468", "suppressedMessages": "1469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1470", "messages": "1471", "suppressedMessages": "1472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1473", "messages": "1474", "suppressedMessages": "1475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1476", "messages": "1477", "suppressedMessages": "1478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1479", "messages": "1480", "suppressedMessages": "1481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1482", "messages": "1483", "suppressedMessages": "1484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1485", "messages": "1486", "suppressedMessages": "1487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1488", "messages": "1489", "suppressedMessages": "1490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1491", "messages": "1492", "suppressedMessages": "1493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1494", "messages": "1495", "suppressedMessages": "1496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1497", "messages": "1498", "suppressedMessages": "1499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1500", "messages": "1501", "suppressedMessages": "1502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1503", "messages": "1504", "suppressedMessages": "1505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1506", "messages": "1507", "suppressedMessages": "1508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1509", "messages": "1510", "suppressedMessages": "1511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1512", "messages": "1513", "suppressedMessages": "1514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1515", "messages": "1516", "suppressedMessages": "1517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1518", "messages": "1519", "suppressedMessages": "1520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1521", "messages": "1522", "suppressedMessages": "1523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1524", "messages": "1525", "suppressedMessages": "1526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1527", "messages": "1528", "suppressedMessages": "1529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1530", "messages": "1531", "suppressedMessages": "1532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1533", "messages": "1534", "suppressedMessages": "1535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1536", "messages": "1537", "suppressedMessages": "1538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1539", "messages": "1540", "suppressedMessages": "1541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1542", "messages": "1543", "suppressedMessages": "1544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1545", "messages": "1546", "suppressedMessages": "1547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1548", "messages": "1549", "suppressedMessages": "1550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1551", "messages": "1552", "suppressedMessages": "1553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1554", "messages": "1555", "suppressedMessages": "1556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1557", "messages": "1558", "suppressedMessages": "1559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1560", "messages": "1561", "suppressedMessages": "1562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1563", "messages": "1564", "suppressedMessages": "1565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1566", "messages": "1567", "suppressedMessages": "1568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1569", "messages": "1570", "suppressedMessages": "1571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1572", "messages": "1573", "suppressedMessages": "1574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1575", "messages": "1576", "suppressedMessages": "1577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1578", "messages": "1579", "suppressedMessages": "1580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1581", "messages": "1582", "suppressedMessages": "1583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1584", "messages": "1585", "suppressedMessages": "1586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1587", "messages": "1588", "suppressedMessages": "1589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1590", "messages": "1591", "suppressedMessages": "1592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1593", "messages": "1594", "suppressedMessages": "1595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1596", "messages": "1597", "suppressedMessages": "1598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1599", "messages": "1600", "suppressedMessages": "1601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1602", "messages": "1603", "suppressedMessages": "1604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1605", "messages": "1606", "suppressedMessages": "1607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1608", "messages": "1609", "suppressedMessages": "1610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1611", "messages": "1612", "suppressedMessages": "1613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1614", "messages": "1615", "suppressedMessages": "1616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1617", "messages": "1618", "suppressedMessages": "1619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1620", "messages": "1621", "suppressedMessages": "1622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1623", "messages": "1624", "suppressedMessages": "1625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1626", "messages": "1627", "suppressedMessages": "1628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1629", "messages": "1630", "suppressedMessages": "1631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1632", "messages": "1633", "suppressedMessages": "1634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1635", "messages": "1636", "suppressedMessages": "1637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1638", "messages": "1639", "suppressedMessages": "1640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1641", "messages": "1642", "suppressedMessages": "1643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1644", "messages": "1645", "suppressedMessages": "1646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1647", "messages": "1648", "suppressedMessages": "1649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1650", "messages": "1651", "suppressedMessages": "1652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1653", "messages": "1654", "suppressedMessages": "1655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1656", "messages": "1657", "suppressedMessages": "1658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1659", "messages": "1660", "suppressedMessages": "1661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1662", "messages": "1663", "suppressedMessages": "1664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1665", "messages": "1666", "suppressedMessages": "1667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1668", "messages": "1669", "suppressedMessages": "1670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1671", "messages": "1672", "suppressedMessages": "1673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1674", "messages": "1675", "suppressedMessages": "1676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1677", "messages": "1678", "suppressedMessages": "1679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1680", "messages": "1681", "suppressedMessages": "1682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1683", "messages": "1684", "suppressedMessages": "1685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1686", "messages": "1687", "suppressedMessages": "1688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1689", "messages": "1690", "suppressedMessages": "1691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1692", "messages": "1693", "suppressedMessages": "1694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1695", "messages": "1696", "suppressedMessages": "1697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\App.jsx", [], ["1698", "1699"], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\store.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\contexts\\interfaceContext.js", [], ["1700"], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\contexts\\sidebarContext.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\AppLayout.jsx", ["1701", "1702", "1703", "1704", "1705", "1706", "1707", "1708", "1709", "1710", "1711", "1712", "1713", "1714", "1715", "1716", "1717", "1718", "1719", "1720", "1721", "1722", "1723"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\styles\\global.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\helpers.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\styles\\mui-theme.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\layout.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\todos.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\messenger.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\cards.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\auth.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\appointments.js", ["1724"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\users.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\chats.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\tasks.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\styles\\vars.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layouts.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\cards.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\todos.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\messenger.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\constants\\app.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Caregivers.jsx", ["1725", "1726"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Tasks.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AddCaregiver.jsx", ["1727", "1728", "1729", "1730", "1731", "1732", "1733", "1734", "1735", "1736", "1737", "1738", "1739"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AddUser.jsx", ["1740", "1741", "1742", "1743", "1744", "1745", "1746", "1747", "1748"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Login.jsx", ["1749", "1750"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\PageNotFound.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Settings.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Finances.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DoctorsReviews.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\PatientMessenger.jsx", ["1751"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DoctorMessenger.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Staff.jsx", ["1752"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Tests.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AppointmentDetails.jsx", ["1753", "1754", "1755", "1756", "1757", "1758", "1759", "1760", "1761", "1762", "1763", "1764", "1765", "1766"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\PatientReviews.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DoctorAppointments.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Appointments.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardK.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardJ.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Nurses.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardI.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardH.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardG.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardF.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardD.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardE.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardC.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardB.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardA.jsx", ["1767", "1768", "1769"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Dashboard.jsx", ["1770", "1771", "1772", "1773", "1774", "1775", "1776"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\config\\firebase.config.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\ProtectedRoutes\\index.jsx", ["1777", "1778"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\PublicRoutes\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\storage.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\constants\\options.js", ["1779"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\useNotistack.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\service\\firebase.service.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\UserSettings\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Widget\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Reviews\\Content.jsx", [], ["1780", "1781"], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Reviews\\List.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Page\\index.jsx", ["1782", "1783", "1784", "1785", "1786"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Widget\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\WidgetsLoader\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CaregiversList\\index.jsx", ["1787", "1788", "1789", "1790"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\TaskPlanner\\index.jsx", ["1791", "1792", "1793", "1794", "1795", "1796", "1797", "1798", "1799", "1800", "1801", "1802", "1803", "1804", "1805", "1806", "1807"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\DropFiles\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\UserSettings\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Widget\\WidgetBody\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\Balance\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CreditCards\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Select\\index.jsx", [], ["1808"], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\MaskedInputs\\Date\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\TabNav\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\MaskedInputs\\Phone\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\TabNav\\TabNavItem\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\ModalWindow\\index.jsx", ["1809"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\index.jsx", ["1810"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PaymentsFeed\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Field\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\LabeledFormInput\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Btn\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\UsersList\\index.jsx", ["1811", "1812", "1813", "1814", "1815", "1816", "1817", "1818"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentListItem\\AppointmentListItem.jsx", ["1819", "1820", "1821", "1822", "1823", "1824", "1825", "1826", "1827", "1828", "1829", "1830", "1831"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsTests\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\StaffList\\index.jsx", ["1832", "1833"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\NoDataPlaceholder\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Avatar\\index.jsx", ["1834"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsList\\index.jsx", ["1835", "1836"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DoctorCalendar\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientCalendar\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\Statistics\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PaymentsHistory\\index.jsx", [], ["1837"], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\RecentTests\\index.jsx", [], ["1838"], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\AppointmentsScheduler\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PaymentsOverview\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DiseaseRate\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsRadialBar\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\RadarAreaChart\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\NursesList\\index.jsx", ["1839", "1840"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DailyPlanner\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\EpiContextPeriodChart\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\RecoveryRate\\index.jsx", [], ["1841"], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\MapChart\\index.jsx", [], ["1842"], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DailyAppointmentChart\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\EpiContextAreaChart\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\HealthIndexChart\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DoctorRatingList\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PainLocation\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DoctorCureRate\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\HepatitisChart\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\TaskScheduler\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsGenderLineChart\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\BloodTest\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\HeartRate\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\EventsCompactCalendar\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DiagnosesDonut\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DailyAppointmentsByDoc\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\usePageIsOverflow.js", [], ["1843"], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\GenderScatter\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\useWindowSize.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\RecentQuestions\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\TasksList\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\ConfirmedDiagnoses\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientOverallAppointments\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientAppointmentsHistory\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\NextPatient\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\UpcomingAppointments\\index.jsx", ["1844", "1845", "1846", "1847", "1848", "1849"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\LaboratoryTests\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DoctorOverallAppointment\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsPace\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Sidebar\\index.jsx", ["1850", "1851", "1852"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Panel\\index.jsx", ["1853", "1854", "1855", "1856", "1857", "1858", "1859", "1860"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\ScrollProgress\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\doctors.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\config\\axios.config.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Reviews\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\doctors_reviews.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\useContentHeight.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\dates.js", ["1861", "1862"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Reviews\\Review.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Page\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\useGenderFilter.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\useArrayNav.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DailyPlanner\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Badge\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\GlobalSettingsControls\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\TextArea\\TextArea.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Navigator\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\ScrollContainer\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\SelectPlaceholder\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\GlobalSettingsControls\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\CustomRating\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\DoctorRatingItem\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Widget\\WidgetHeader\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Grid\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CaregiversList\\Group\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\GenderNav\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\SearchBar\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\AssignShiftModal\\index.jsx", ["1863", "1864", "1865", "1866", "1867", "1868", "1869", "1870", "1871", "1872", "1873", "1874", "1875", "1876", "1877", "1878", "1879", "1880", "1881", "1882"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\UserSettings\\Form\\index.jsx", ["1883"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\PersonList\\Item\\index.jsx", ["1884", "1885", "1886", "1887", "1888", "1889", "1890", "1891", "1892"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\AddForm\\index.jsx", ["1893", "1894", "1895", "1896", "1897", "1898", "1899", "1900", "1901", "1902", "1903", "1904", "1905", "1906", "1907", "1908", "1909", "1910"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\TodosLegend\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\DnDLayout\\index.jsx", ["1911", "1912"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\Balance\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CreditCards\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Select\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\TabNav\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\payments.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PaymentsFeed\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\MaskedInputs\\Date\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentListItem\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsTests\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CreditCards\\Card\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CreditCards\\Form\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\Main\\index.jsx", ["1913", "1914"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\UsersList\\User\\index.jsx", ["1915", "1916", "1917", "1918", "1919", "1920"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PaymentsFeed\\Payments\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\patient_tests.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\staff.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentListItem\\AppointmentChip.jsx", ["1921"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentListItem\\AppointmentStatus.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\constants\\framer.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\Statistics\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsList\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\payments_history.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\constants\\colors.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PaymentsOverview\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\AppointmentsScheduler\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\MapChart\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\usePeriodNav.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\disease.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\numbers.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\epi_period.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\HealthIndexChart\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Reminder\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\ActionButton\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\ShapeButton\\index.jsx", ["1922"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\TestItem\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\GroupSeparator\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\health.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\index.jsx", ["1923", "1924", "1925", "1926", "1927", "1928", "1929", "1930", "1931", "1932", "1933", "1934", "1935", "1936", "1937", "1938", "1939"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PainLocation\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\cure.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\TaskScheduler\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Navigator\\MonthNavigator\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\StaffList\\Group\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsList\\Group\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\PaymentItem\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\DailyNavigation\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\scheduler.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\Event\\index.jsx", ["1940", "1941", "1942", "1943", "1944", "1945"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\SelectableSlot\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Legend\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\LabeledProgress\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\PeriodNav\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\HeartRate\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\gender.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\EventsCompactCalendar\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Legend\\LegendItem\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\scatter.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\RecentQuestions\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\confirmed.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\appointments.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Widget\\WidgetNav\\index.jsx", ["1946"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\NursesList\\Group\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\CartesianChart\\index.jsx", [], ["1947"], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Progress\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Timestamp\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PainLocation\\Body\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\ModalWindow\\CloseBtn\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DiagnosesDonut\\ChartLabel\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Chart\\Tooltip\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Chart\\Labels\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\OverallAppointmentChart\\ChartLegend\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\OverallAppointmentChart\\ChartBars\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\NextPatient\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\LaboratoryTests\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\upcoming.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\UpcomingAppointments\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\RecentQuestions\\RecentQuestionsItem\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Truncated.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\ConfirmedDiagnoses\\List\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentItem\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentItem\\index.jsx", ["1948", "1949"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\usePanelScroll.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Panel\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Sidebar\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\MenuDots\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\IconLink\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Pill\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Sidebar\\Content\\index.jsx", ["1950"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Sidebar\\Emergency\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\MenuButton\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Logo\\index.jsx", ["1951", "1952"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Panel\\CurrentUser\\index.jsx", ["1953", "1954"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Navigator\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\DoctorRatingItem\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\useElementScroll.js", [], ["1955"], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\AssignShiftModal\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\RangeSlider\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\PersonList\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\ActionButton\\ChatBtn.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\PersonList\\Item\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\ActionButton\\TextBtn.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\ActionButton\\EditBtn.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\DnDLayout\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\AddForm\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\Todo\\index.jsx", ["1956", "1957", "1958", "1959", "1960"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\ConfirmationModal\\index.jsx", ["1961", "1962", "1963", "1964"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\styles\\keyframes.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CreditCards\\Card\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CreditCards\\Form\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PaymentsFeed\\Payments\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\UsersList\\User\\style.js", ["1965"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\TestItem\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\calendar_appointments.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Radio\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\Message\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\Header\\index.jsx", ["1966", "1967", "1968"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\Input\\index.jsx", ["1969", "1970", "1971", "1972", "1973", "1974", "1975"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\TimeSlot\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\WeeklyNavigation\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\DailyToolbar\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\WeekSelector\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\MonthlyNavigation\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\MonthSelector\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\DoctorPopup\\index.jsx", ["1976", "1977", "1978", "1979", "1980", "1981", "1982", "1983", "1984", "1985"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\ScheduleAppointmentModal\\index.jsx", ["1986", "1987", "1988", "1989"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Legend\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\Event\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\PaymentItem\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\recharts.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\OverallAppointmentChart\\ChartBars\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\CustomTooltip\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\ConfirmedDiagnoses\\List\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Sidebar\\Emergency\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\constants\\menu.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\Todo\\planner.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\Todo\\list.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\Message\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\Header\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Checkbox\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\DoctorPopup\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Waveform\\index.jsx", [], ["1990"], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\notifications.js", ["1991", "1992"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Notifications.jsx", ["1993", "1994", "1995", "1996"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AddPatient.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\PatientDetails.jsx", ["1997", "1998", "1999", "2000", "2001", "2002", "2003", "2004", "2005", "2006", "2007", "2008", "2009"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Patients.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AddPatientStep1.jsx", ["2010", "2011", "2012", "2013"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\NotificationItem\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AddPatientStep2.jsx", ["2014", "2015", "2016"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AddPatientStep4.jsx", ["2017", "2018"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AddPatientStep3.jsx", ["2019"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\CircularProgress\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\RecentPatients\\index.jsx", ["2020", "2021", "2022"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\TopStaff\\index.jsx", ["2023", "2024"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\StaffDetails.jsx", ["2025", "2026", "2027", "2028", "2029", "2030", "2031", "2032", "2033", "2034", "2035", "2036"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\appointmentValidation.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentActionButtons\\index.jsx", ["2037", "2038", "2039", "2040", "2041", "2042", "2043"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\useScrollLock.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\MobileAppointmentsList.jsx", ["2044"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\recurringAppointmentHelpers.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\MobileCalendar\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\MobileCalendar\\style.js", ["2045"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\selectors\\chatSelectors.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\phoneValidation.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\PhoneNumberInput\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\VisitNotesSidebar\\index.jsx", ["2046", "2047", "2048", "2049", "2050", "2051"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\AddressInput\\index.jsx", ["2052", "2053", "2054"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AssignAdminModal\\index.jsx", ["2055", "2056", "2057"], [], {"ruleId": "2058", "severity": 1, "message": "2059", "line": 47, "column": 6, "nodeType": "2060", "endLine": 47, "endColumn": 17, "suggestions": "2061", "suppressions": "2062"}, {"ruleId": "2058", "severity": 1, "message": "2063", "line": 53, "column": 6, "nodeType": "2060", "endLine": 53, "endColumn": 8, "suggestions": "2064", "suppressions": "2065"}, {"ruleId": "2058", "severity": 1, "message": "2066", "line": 73, "column": 6, "nodeType": "2060", "endLine": 73, "endColumn": 81, "suggestions": "2067", "suppressions": "2068"}, {"ruleId": "2069", "severity": 1, "message": "2070", "line": 9, "column": 16, "nodeType": "2071", "messageId": "2072", "endLine": 9, "endColumn": 25}, {"ruleId": "2069", "severity": 1, "message": "2073", "line": 19, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 19, "endColumn": 33}, {"ruleId": "2069", "severity": 1, "message": "2074", "line": 19, "column": 35, "nodeType": "2071", "messageId": "2072", "endLine": 19, "endColumn": 58}, {"ruleId": "2069", "severity": 1, "message": "2075", "line": 20, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 20, "endColumn": 19}, {"ruleId": "2069", "severity": 1, "message": "2076", "line": 25, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 25, "endColumn": 17}, {"ruleId": "2069", "severity": 1, "message": "2077", "line": 26, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 26, "endColumn": 17}, {"ruleId": "2069", "severity": 1, "message": "2078", "line": 27, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 27, "endColumn": 17}, {"ruleId": "2069", "severity": 1, "message": "2079", "line": 28, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 28, "endColumn": 17}, {"ruleId": "2069", "severity": 1, "message": "2080", "line": 29, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 29, "endColumn": 17}, {"ruleId": "2069", "severity": 1, "message": "2081", "line": 30, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 30, "endColumn": 17}, {"ruleId": "2069", "severity": 1, "message": "2082", "line": 31, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 31, "endColumn": 17}, {"ruleId": "2069", "severity": 1, "message": "2083", "line": 32, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 32, "endColumn": 17}, {"ruleId": "2069", "severity": 1, "message": "2084", "line": 33, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 33, "endColumn": 17}, {"ruleId": "2069", "severity": 1, "message": "2085", "line": 34, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 34, "endColumn": 17}, {"ruleId": "2069", "severity": 1, "message": "2086", "line": 35, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 35, "endColumn": 17}, {"ruleId": "2069", "severity": 1, "message": "2087", "line": 36, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 36, "endColumn": 25}, {"ruleId": "2069", "severity": 1, "message": "2088", "line": 43, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 43, "endColumn": 12}, {"ruleId": "2069", "severity": 1, "message": "2089", "line": 47, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 47, "endColumn": 21}, {"ruleId": "2069", "severity": 1, "message": "2090", "line": 48, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 48, "endColumn": 21}, {"ruleId": "2069", "severity": 1, "message": "2091", "line": 49, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 49, "endColumn": 15}, {"ruleId": "2069", "severity": 1, "message": "2092", "line": 51, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 51, "endColumn": 19}, {"ruleId": "2069", "severity": 1, "message": "2093", "line": 55, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 55, "endColumn": 19}, {"ruleId": "2058", "severity": 1, "message": "2094", "line": 102, "column": 6, "nodeType": "2060", "endLine": 102, "endColumn": 12, "suggestions": "2095"}, {"ruleId": "2069", "severity": 1, "message": "2096", "line": 3, "column": 27, "nodeType": "2071", "messageId": "2072", "endLine": 3, "endColumn": 33}, {"ruleId": "2069", "severity": 1, "message": "2097", "line": 11, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 11, "endColumn": 13}, {"ruleId": "2069", "severity": 1, "message": "2098", "line": 19, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 19, "endColumn": 17}, {"ruleId": "2069", "severity": 1, "message": "2099", "line": 12, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 12, "endColumn": 13}, {"ruleId": "2069", "severity": 1, "message": "2100", "line": 13, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 13, "endColumn": 24}, {"ruleId": "2069", "severity": 1, "message": "2101", "line": 16, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 16, "endColumn": 17}, {"ruleId": "2069", "severity": 1, "message": "2102", "line": 55, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 55, "endColumn": 18}, {"ruleId": "2069", "severity": 1, "message": "2103", "line": 59, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 59, "endColumn": 20}, {"ruleId": "2069", "severity": 1, "message": "2104", "line": 76, "column": 5, "nodeType": "2071", "messageId": "2072", "endLine": 76, "endColumn": 17}, {"ruleId": "2069", "severity": 1, "message": "2105", "line": 77, "column": 5, "nodeType": "2071", "messageId": "2072", "endLine": 77, "endColumn": 12}, {"ruleId": "2069", "severity": 1, "message": "2106", "line": 78, "column": 18, "nodeType": "2071", "messageId": "2072", "endLine": 78, "endColumn": 24}, {"ruleId": "2069", "severity": 1, "message": "2107", "line": 78, "column": 26, "nodeType": "2071", "messageId": "2072", "endLine": 78, "endColumn": 38}, {"ruleId": "2069", "severity": 1, "message": "2108", "line": 81, "column": 5, "nodeType": "2071", "messageId": "2072", "endLine": 81, "endColumn": 10}, {"ruleId": "2069", "severity": 1, "message": "2109", "line": 97, "column": 12, "nodeType": "2071", "messageId": "2072", "endLine": 97, "endColumn": 25}, {"ruleId": "2069", "severity": 1, "message": "2110", "line": 102, "column": 18, "nodeType": "2071", "messageId": "2072", "endLine": 102, "endColumn": 28}, {"ruleId": "2058", "severity": 1, "message": "2111", "line": 152, "column": 6, "nodeType": "2060", "endLine": 152, "endColumn": 26, "suggestions": "2112"}, {"ruleId": "2069", "severity": 1, "message": "2113", "line": 19, "column": 15, "nodeType": "2071", "messageId": "2072", "endLine": 19, "endColumn": 26}, {"ruleId": "2069", "severity": 1, "message": "2099", "line": 42, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 42, "endColumn": 13}, {"ruleId": "2069", "severity": 1, "message": "2114", "line": 178, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 178, "endColumn": 17}, {"ruleId": "2058", "severity": 1, "message": "2115", "line": 568, "column": 6, "nodeType": "2060", "endLine": 568, "endColumn": 25, "suggestions": "2116"}, {"ruleId": "2058", "severity": 1, "message": "2117", "line": 577, "column": 6, "nodeType": "2060", "endLine": 577, "endColumn": 17, "suggestions": "2118"}, {"ruleId": "2119", "severity": 1, "message": "2120", "line": 745, "column": 38, "nodeType": "2121", "messageId": "2122", "endLine": 745, "endColumn": 40}, {"ruleId": "2123", "severity": 1, "message": "2124", "line": 904, "column": 31, "nodeType": "2125", "endLine": 904, "endColumn": 62}, {"ruleId": "2123", "severity": 1, "message": "2124", "line": 931, "column": 31, "nodeType": "2125", "endLine": 931, "endColumn": 61}, {"ruleId": "2123", "severity": 1, "message": "2124", "line": 1137, "column": 13, "nodeType": "2125", "endLine": 1140, "endColumn": 15}, {"ruleId": "2069", "severity": 1, "message": "2126", "line": 19, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 19, "endColumn": 13}, {"ruleId": "2069", "severity": 1, "message": "2127", "line": 28, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 28, "endColumn": 22}, {"ruleId": "2058", "severity": 1, "message": "2128", "line": 54, "column": 6, "nodeType": "2060", "endLine": 54, "endColumn": 18, "suggestions": "2129"}, {"ruleId": "2069", "severity": 1, "message": "2130", "line": 10, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 10, "endColumn": 28}, {"ruleId": "2069", "severity": 1, "message": "2131", "line": 1, "column": 17, "nodeType": "2071", "messageId": "2072", "endLine": 1, "endColumn": 26}, {"ruleId": "2069", "severity": 1, "message": "2132", "line": 5, "column": 33, "nodeType": "2071", "messageId": "2072", "endLine": 5, "endColumn": 43}, {"ruleId": "2069", "severity": 1, "message": "2133", "line": 6, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 6, "endColumn": 19}, {"ruleId": "2069", "severity": 1, "message": "2134", "line": 14, "column": 28, "nodeType": "2071", "messageId": "2072", "endLine": 14, "endColumn": 40}, {"ruleId": "2069", "severity": 1, "message": "2135", "line": 16, "column": 28, "nodeType": "2071", "messageId": "2072", "endLine": 16, "endColumn": 40}, {"ruleId": "2069", "severity": 1, "message": "2136", "line": 21, "column": 3, "nodeType": "2071", "messageId": "2072", "endLine": 21, "endColumn": 25}, {"ruleId": "2069", "severity": 1, "message": "2137", "line": 28, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 28, "endColumn": 12}, {"ruleId": "2069", "severity": 1, "message": "2138", "line": 29, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 29, "endColumn": 16}, {"ruleId": "2069", "severity": 1, "message": "2139", "line": 30, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 30, "endColumn": 16}, {"ruleId": "2069", "severity": 1, "message": "2140", "line": 139, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 139, "endColumn": 22}, {"ruleId": "2069", "severity": 1, "message": "2141", "line": 214, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 214, "endColumn": 17}, {"ruleId": "2069", "severity": 1, "message": "2142", "line": 253, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 253, "endColumn": 24}, {"ruleId": "2069", "severity": 1, "message": "2143", "line": 257, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 257, "endColumn": 19}, {"ruleId": "2069", "severity": 1, "message": "2144", "line": 281, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 281, "endColumn": 29}, {"ruleId": "2069", "severity": 1, "message": "2145", "line": 10, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 10, "endColumn": 29}, {"ruleId": "2069", "severity": 1, "message": "2146", "line": 11, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 11, "endColumn": 34}, {"ruleId": "2069", "severity": 1, "message": "2147", "line": 12, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 12, "endColumn": 25}, {"ruleId": "2069", "severity": 1, "message": "2148", "line": 3, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 3, "endColumn": 26}, {"ruleId": "2069", "severity": 1, "message": "2145", "line": 4, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 4, "endColumn": 29}, {"ruleId": "2069", "severity": 1, "message": "2149", "line": 5, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 5, "endColumn": 30}, {"ruleId": "2069", "severity": 1, "message": "2150", "line": 6, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 6, "endColumn": 22}, {"ruleId": "2069", "severity": 1, "message": "2151", "line": 7, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 7, "endColumn": 34}, {"ruleId": "2069", "severity": 1, "message": "2146", "line": 8, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 8, "endColumn": 34}, {"ruleId": "2069", "severity": 1, "message": "2152", "line": 9, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 9, "endColumn": 22}, {"ruleId": "2069", "severity": 1, "message": "2153", "line": 2, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 2, "endColumn": 21}, {"ruleId": "2069", "severity": 1, "message": "2154", "line": 6, "column": 36, "nodeType": "2071", "messageId": "2072", "endLine": 6, "endColumn": 44}, {"ruleId": "2069", "severity": 1, "message": "2155", "line": 3, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 3, "endColumn": 16}, {"ruleId": "2058", "severity": 1, "message": "2156", "line": 75, "column": 8, "nodeType": "2060", "endLine": 75, "endColumn": 21, "suggestions": "2157", "suppressions": "2158"}, {"ruleId": "2058", "severity": 1, "message": "2159", "line": 80, "column": 8, "nodeType": "2060", "endLine": 80, "endColumn": 30, "suggestions": "2160", "suppressions": "2161"}, {"ruleId": "2069", "severity": 1, "message": "2162", "line": 9, "column": 3, "nodeType": "2071", "messageId": "2072", "endLine": 9, "endColumn": 18}, {"ruleId": "2069", "severity": 1, "message": "2163", "line": 11, "column": 3, "nodeType": "2071", "messageId": "2072", "endLine": 11, "endColumn": 16}, {"ruleId": "2069", "severity": 1, "message": "2164", "line": 12, "column": 3, "nodeType": "2071", "messageId": "2072", "endLine": 12, "endColumn": 15}, {"ruleId": "2069", "severity": 1, "message": "2165", "line": 14, "column": 3, "nodeType": "2071", "messageId": "2072", "endLine": 14, "endColumn": 19}, {"ruleId": "2069", "severity": 1, "message": "2166", "line": 39, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 39, "endColumn": 20}, {"ruleId": "2069", "severity": 1, "message": "2167", "line": 11, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 11, "endColumn": 20}, {"ruleId": "2069", "severity": 1, "message": "2168", "line": 18, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 18, "endColumn": 21}, {"ruleId": "2069", "severity": 1, "message": "2169", "line": 26, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 26, "endColumn": 12}, {"ruleId": "2069", "severity": 1, "message": "2170", "line": 62, "column": 20, "nodeType": "2071", "messageId": "2072", "endLine": 62, "endColumn": 31}, {"ruleId": "2069", "severity": 1, "message": "2171", "line": 9, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 9, "endColumn": 19}, {"ruleId": "2069", "severity": 1, "message": "2155", "line": 18, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 18, "endColumn": 16}, {"ruleId": "2069", "severity": 1, "message": "2172", "line": 19, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 19, "endColumn": 11}, {"ruleId": "2069", "severity": 1, "message": "2173", "line": 20, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 20, "endColumn": 13}, {"ruleId": "2069", "severity": 1, "message": "2174", "line": 20, "column": 15, "nodeType": "2071", "messageId": "2072", "endLine": 20, "endColumn": 21}, {"ruleId": "2069", "severity": 1, "message": "2175", "line": 20, "column": 23, "nodeType": "2071", "messageId": "2072", "endLine": 20, "endColumn": 32}, {"ruleId": "2069", "severity": 1, "message": "2176", "line": 21, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 21, "endColumn": 12}, {"ruleId": "2069", "severity": 1, "message": "2177", "line": 22, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 22, "endColumn": 21}, {"ruleId": "2069", "severity": 1, "message": "2178", "line": 24, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 24, "endColumn": 29}, {"ruleId": "2069", "severity": 1, "message": "2179", "line": 27, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 27, "endColumn": 14}, {"ruleId": "2069", "severity": 1, "message": "2180", "line": 28, "column": 18, "nodeType": "2071", "messageId": "2072", "endLine": 28, "endColumn": 32}, {"ruleId": "2069", "severity": 1, "message": "2181", "line": 28, "column": 34, "nodeType": "2071", "messageId": "2072", "endLine": 28, "endColumn": 50}, {"ruleId": "2069", "severity": 1, "message": "2182", "line": 28, "column": 52, "nodeType": "2071", "messageId": "2072", "endLine": 28, "endColumn": 64}, {"ruleId": "2069", "severity": 1, "message": "2183", "line": 29, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 29, "endColumn": 19}, {"ruleId": "2069", "severity": 1, "message": "2184", "line": 29, "column": 21, "nodeType": "2071", "messageId": "2072", "endLine": 29, "endColumn": 31}, {"ruleId": "2069", "severity": 1, "message": "2185", "line": 30, "column": 11, "nodeType": "2071", "messageId": "2072", "endLine": 30, "endColumn": 26}, {"ruleId": "2069", "severity": 1, "message": "2141", "line": 31, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 31, "endColumn": 17}, {"ruleId": "2058", "severity": 1, "message": "2186", "line": 27, "column": 6, "nodeType": "2060", "endLine": 27, "endColumn": 8, "suggestions": "2187", "suppressions": "2188"}, {"ruleId": "2069", "severity": 1, "message": "2131", "line": 10, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 10, "endColumn": 19}, {"ruleId": "2069", "severity": 1, "message": "2189", "line": 4, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 4, "endColumn": 25}, {"ruleId": "2069", "severity": 1, "message": "2190", "line": 21, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 21, "endColumn": 16}, {"ruleId": "2069", "severity": 1, "message": "2191", "line": 21, "column": 18, "nodeType": "2071", "messageId": "2072", "endLine": 21, "endColumn": 25}, {"ruleId": "2069", "severity": 1, "message": "2192", "line": 23, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 23, "endColumn": 13}, {"ruleId": "2069", "severity": 1, "message": "2193", "line": 24, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 24, "endColumn": 21}, {"ruleId": "2069", "severity": 1, "message": "2194", "line": 29, "column": 23, "nodeType": "2071", "messageId": "2072", "endLine": 29, "endColumn": 37}, {"ruleId": "2069", "severity": 1, "message": "2195", "line": 30, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 30, "endColumn": 23}, {"ruleId": "2069", "severity": 1, "message": "2196", "line": 45, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 45, "endColumn": 17}, {"ruleId": "2058", "severity": 1, "message": "2094", "line": 93, "column": 6, "nodeType": "2060", "endLine": 93, "endColumn": 26, "suggestions": "2197"}, {"ruleId": "2069", "severity": 1, "message": "2198", "line": 4, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 4, "endColumn": 16}, {"ruleId": "2069", "severity": 1, "message": "2199", "line": 5, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 5, "endColumn": 20}, {"ruleId": "2069", "severity": 1, "message": "2200", "line": 6, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 6, "endColumn": 19}, {"ruleId": "2069", "severity": 1, "message": "2201", "line": 8, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 8, "endColumn": 14}, {"ruleId": "2069", "severity": 1, "message": "2202", "line": 9, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 9, "endColumn": 16}, {"ruleId": "2069", "severity": 1, "message": "2203", "line": 9, "column": 18, "nodeType": "2071", "messageId": "2072", "endLine": 9, "endColumn": 22}, {"ruleId": "2069", "severity": 1, "message": "2204", "line": 9, "column": 24, "nodeType": "2071", "messageId": "2072", "endLine": 9, "endColumn": 28}, {"ruleId": "2069", "severity": 1, "message": "2205", "line": 9, "column": 30, "nodeType": "2071", "messageId": "2072", "endLine": 9, "endColumn": 35}, {"ruleId": "2069", "severity": 1, "message": "2206", "line": 9, "column": 37, "nodeType": "2071", "messageId": "2072", "endLine": 9, "endColumn": 42}, {"ruleId": "2069", "severity": 1, "message": "2207", "line": 9, "column": 44, "nodeType": "2071", "messageId": "2072", "endLine": 9, "endColumn": 53}, {"ruleId": "2069", "severity": 1, "message": "2208", "line": 10, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 10, "endColumn": 13}, {"ruleId": "2069", "severity": 1, "message": "2209", "line": 11, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 11, "endColumn": 23}, {"ruleId": "2069", "severity": 1, "message": "2172", "line": 13, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 13, "endColumn": 11}, {"ruleId": "2069", "severity": 1, "message": "2210", "line": 25, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 25, "endColumn": 20}, {"ruleId": "2069", "severity": 1, "message": "2169", "line": 27, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 27, "endColumn": 12}, {"ruleId": "2069", "severity": 1, "message": "2211", "line": 8, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 8, "endColumn": 19}, {"ruleId": "2069", "severity": 1, "message": "2212", "line": 9, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 9, "endColumn": 22}, {"ruleId": "2069", "severity": 1, "message": "2213", "line": 31, "column": 17, "nodeType": "2071", "messageId": "2072", "endLine": 31, "endColumn": 25}, {"ruleId": "2058", "severity": 1, "message": "2214", "line": 66, "column": 8, "nodeType": "2060", "endLine": 66, "endColumn": 20, "suggestions": "2215", "suppressions": "2216"}, {"ruleId": "2058", "severity": 1, "message": "2214", "line": 45, "column": 8, "nodeType": "2060", "endLine": 45, "endColumn": 20, "suggestions": "2217", "suppressions": "2218"}, {"ruleId": "2069", "severity": 1, "message": "2210", "line": 25, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 25, "endColumn": 20}, {"ruleId": "2069", "severity": 1, "message": "2169", "line": 27, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 27, "endColumn": 12}, {"ruleId": "2058", "severity": 1, "message": "2219", "line": 47, "column": 6, "nodeType": "2060", "endLine": 47, "endColumn": 8, "suggestions": "2220", "suppressions": "2221"}, {"ruleId": "2058", "severity": 1, "message": "2222", "line": 50, "column": 8, "nodeType": "2060", "endLine": 50, "endColumn": 15, "suggestions": "2223", "suppressions": "2224"}, {"ruleId": "2058", "severity": 1, "message": "2225", "line": 20, "column": 8, "nodeType": "2060", "endLine": 20, "endColumn": 10, "suggestions": "2226", "suppressions": "2227"}, {"ruleId": "2069", "severity": 1, "message": "2228", "line": 2, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 2, "endColumn": 28}, {"ruleId": "2069", "severity": 1, "message": "2229", "line": 9, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 9, "endColumn": 23}, {"ruleId": "2069", "severity": 1, "message": "2230", "line": 10, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 10, "endColumn": 30}, {"ruleId": "2069", "severity": 1, "message": "2231", "line": 11, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 11, "endColumn": 20}, {"ruleId": "2069", "severity": 1, "message": "2131", "line": 19, "column": 18, "nodeType": "2071", "messageId": "2072", "endLine": 19, "endColumn": 27}, {"ruleId": "2069", "severity": 1, "message": "2141", "line": 26, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 26, "endColumn": 17}, {"ruleId": "2069", "severity": 1, "message": "2232", "line": 12, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 12, "endColumn": 17}, {"ruleId": "2069", "severity": 1, "message": "2233", "line": 13, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 13, "endColumn": 18}, {"ruleId": "2069", "severity": 1, "message": "2234", "line": 30, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 30, "endColumn": 20}, {"ruleId": "2069", "severity": 1, "message": "2235", "line": 2, "column": 27, "nodeType": "2071", "messageId": "2072", "endLine": 2, "endColumn": 32}, {"ruleId": "2069", "severity": 1, "message": "2236", "line": 2, "column": 34, "nodeType": "2071", "messageId": "2072", "endLine": 2, "endColumn": 39}, {"ruleId": "2069", "severity": 1, "message": "2237", "line": 2, "column": 41, "nodeType": "2071", "messageId": "2072", "endLine": 2, "endColumn": 47}, {"ruleId": "2069", "severity": 1, "message": "2200", "line": 7, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 7, "endColumn": 19}, {"ruleId": "2069", "severity": 1, "message": "2192", "line": 16, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 16, "endColumn": 13}, {"ruleId": "2069", "severity": 1, "message": "2098", "line": 86, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 86, "endColumn": 17}, {"ruleId": "2069", "severity": 1, "message": "2238", "line": 87, "column": 11, "nodeType": "2071", "messageId": "2072", "endLine": 87, "endColumn": 15}, {"ruleId": "2069", "severity": 1, "message": "2239", "line": 100, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 100, "endColumn": 20}, {"ruleId": "2069", "severity": 1, "message": "2240", "line": 73, "column": 11, "nodeType": "2071", "messageId": "2072", "endLine": 73, "endColumn": 26}, {"ruleId": "2069", "severity": 1, "message": "2241", "line": 74, "column": 11, "nodeType": "2071", "messageId": "2072", "endLine": 74, "endColumn": 16}, {"ruleId": "2069", "severity": 1, "message": "2235", "line": 3, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 3, "endColumn": 15}, {"ruleId": "2069", "severity": 1, "message": "2174", "line": 21, "column": 15, "nodeType": "2071", "messageId": "2072", "endLine": 21, "endColumn": 21}, {"ruleId": "2069", "severity": 1, "message": "2242", "line": 21, "column": 23, "nodeType": "2071", "messageId": "2072", "endLine": 21, "endColumn": 32}, {"ruleId": "2069", "severity": 1, "message": "2155", "line": 24, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 24, "endColumn": 16}, {"ruleId": "2069", "severity": 1, "message": "2243", "line": 157, "column": 15, "nodeType": "2071", "messageId": "2072", "endLine": 157, "endColumn": 28}, {"ruleId": "2069", "severity": 1, "message": "2244", "line": 158, "column": 15, "nodeType": "2071", "messageId": "2072", "endLine": 158, "endColumn": 26}, {"ruleId": "2069", "severity": 1, "message": "2245", "line": 204, "column": 5, "nodeType": "2071", "messageId": "2072", "endLine": 204, "endColumn": 13}, {"ruleId": "2069", "severity": 1, "message": "2246", "line": 474, "column": 51, "nodeType": "2071", "messageId": "2072", "endLine": 474, "endColumn": 65}, {"ruleId": "2069", "severity": 1, "message": "2247", "line": 474, "column": 74, "nodeType": "2071", "messageId": "2072", "endLine": 474, "endColumn": 91}, {"ruleId": "2058", "severity": 1, "message": "2248", "line": 651, "column": 6, "nodeType": "2060", "endLine": 651, "endColumn": 12, "suggestions": "2249"}, {"ruleId": "2058", "severity": 1, "message": "2250", "line": 670, "column": 6, "nodeType": "2060", "endLine": 670, "endColumn": 30, "suggestions": "2251"}, {"ruleId": "2058", "severity": 1, "message": "2252", "line": 670, "column": 7, "nodeType": "2253", "endLine": 670, "endColumn": 29}, {"ruleId": "2058", "severity": 1, "message": "2250", "line": 677, "column": 6, "nodeType": "2060", "endLine": 677, "endColumn": 36, "suggestions": "2254"}, {"ruleId": "2058", "severity": 1, "message": "2252", "line": 677, "column": 7, "nodeType": "2253", "endLine": 677, "endColumn": 35}, {"ruleId": "2058", "severity": 1, "message": "2255", "line": 684, "column": 6, "nodeType": "2060", "endLine": 684, "endColumn": 12, "suggestions": "2256"}, {"ruleId": "2058", "severity": 1, "message": "2250", "line": 715, "column": 6, "nodeType": "2060", "endLine": 715, "endColumn": 94, "suggestions": "2257"}, {"ruleId": "2058", "severity": 1, "message": "2252", "line": 715, "column": 7, "nodeType": "2253", "endLine": 715, "endColumn": 25}, {"ruleId": "2058", "severity": 1, "message": "2252", "line": 715, "column": 27, "nodeType": "2253", "endLine": 715, "endColumn": 43}, {"ruleId": "2058", "severity": 1, "message": "2252", "line": 715, "column": 45, "nodeType": "2253", "endLine": 715, "endColumn": 63}, {"ruleId": "2058", "severity": 1, "message": "2252", "line": 715, "column": 65, "nodeType": "2253", "endLine": 715, "endColumn": 93}, {"ruleId": "2069", "severity": 1, "message": "2099", "line": 11, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 11, "endColumn": 13}, {"ruleId": "2069", "severity": 1, "message": "2200", "line": 6, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 6, "endColumn": 19}, {"ruleId": "2069", "severity": 1, "message": "2199", "line": 7, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 7, "endColumn": 20}, {"ruleId": "2069", "severity": 1, "message": "2258", "line": 10, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 10, "endColumn": 20}, {"ruleId": "2069", "severity": 1, "message": "2259", "line": 23, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 23, "endColumn": 15}, {"ruleId": "2069", "severity": 1, "message": "2260", "line": 24, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 24, "endColumn": 15}, {"ruleId": "2069", "severity": 1, "message": "2172", "line": 25, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 25, "endColumn": 11}, {"ruleId": "2069", "severity": 1, "message": "2261", "line": 27, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 27, "endColumn": 21}, {"ruleId": "2069", "severity": 1, "message": "2262", "line": 27, "column": 44, "nodeType": "2071", "messageId": "2072", "endLine": 27, "endColumn": 53}, {"ruleId": "2069", "severity": 1, "message": "2263", "line": 138, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 138, "endColumn": 13}, {"ruleId": "2069", "severity": 1, "message": "2264", "line": 2, "column": 28, "nodeType": "2071", "messageId": "2072", "endLine": 2, "endColumn": 40}, {"ruleId": "2069", "severity": 1, "message": "2167", "line": 5, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 5, "endColumn": 20}, {"ruleId": "2069", "severity": 1, "message": "2265", "line": 6, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 6, "endColumn": 13}, {"ruleId": "2069", "severity": 1, "message": "2266", "line": 13, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 13, "endColumn": 17}, {"ruleId": "2069", "severity": 1, "message": "2267", "line": 13, "column": 19, "nodeType": "2071", "messageId": "2072", "endLine": 13, "endColumn": 33}, {"ruleId": "2069", "severity": 1, "message": "2268", "line": 14, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 14, "endColumn": 22}, {"ruleId": "2069", "severity": 1, "message": "2269", "line": 17, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 17, "endColumn": 16}, {"ruleId": "2069", "severity": 1, "message": "2270", "line": 17, "column": 18, "nodeType": "2071", "messageId": "2072", "endLine": 17, "endColumn": 28}, {"ruleId": "2069", "severity": 1, "message": "2271", "line": 20, "column": 28, "nodeType": "2071", "messageId": "2072", "endLine": 20, "endColumn": 44}, {"ruleId": "2069", "severity": 1, "message": "2272", "line": 20, "column": 46, "nodeType": "2071", "messageId": "2072", "endLine": 20, "endColumn": 67}, {"ruleId": "2069", "severity": 1, "message": "2273", "line": 20, "column": 69, "nodeType": "2071", "messageId": "2072", "endLine": 20, "endColumn": 89}, {"ruleId": "2069", "severity": 1, "message": "2274", "line": 21, "column": 15, "nodeType": "2071", "messageId": "2072", "endLine": 21, "endColumn": 21}, {"ruleId": "2069", "severity": 1, "message": "2132", "line": 21, "column": 23, "nodeType": "2071", "messageId": "2072", "endLine": 21, "endColumn": 33}, {"ruleId": "2069", "severity": 1, "message": "2275", "line": 22, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 22, "endColumn": 13}, {"ruleId": "2069", "severity": 1, "message": "2182", "line": 26, "column": 11, "nodeType": "2071", "messageId": "2072", "endLine": 26, "endColumn": 23}, {"ruleId": "2069", "severity": 1, "message": "2181", "line": 26, "column": 25, "nodeType": "2071", "messageId": "2072", "endLine": 26, "endColumn": 41}, {"ruleId": "2069", "severity": 1, "message": "2180", "line": 26, "column": 43, "nodeType": "2071", "messageId": "2072", "endLine": 26, "endColumn": 57}, {"ruleId": "2069", "severity": 1, "message": "2276", "line": 26, "column": 59, "nodeType": "2071", "messageId": "2072", "endLine": 26, "endColumn": 71}, {"ruleId": "2069", "severity": 1, "message": "2277", "line": 12, "column": 23, "nodeType": "2071", "messageId": "2072", "endLine": 12, "endColumn": 34}, {"ruleId": "2069", "severity": 1, "message": "2278", "line": 20, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 20, "endColumn": 15}, {"ruleId": "2069", "severity": 1, "message": "2279", "line": 20, "column": 22, "nodeType": "2071", "messageId": "2072", "endLine": 20, "endColumn": 29}, {"ruleId": "2058", "severity": 1, "message": "2280", "line": 69, "column": 6, "nodeType": "2060", "endLine": 69, "endColumn": 12, "suggestions": "2281"}, {"ruleId": "2069", "severity": 1, "message": "2233", "line": 6, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 6, "endColumn": 18}, {"ruleId": "2069", "severity": 1, "message": "2270", "line": 12, "column": 26, "nodeType": "2071", "messageId": "2072", "endLine": 12, "endColumn": 36}, {"ruleId": "2069", "severity": 1, "message": "2282", "line": 12, "column": 38, "nodeType": "2071", "messageId": "2072", "endLine": 12, "endColumn": 43}, {"ruleId": "2069", "severity": 1, "message": "2283", "line": 12, "column": 45, "nodeType": "2071", "messageId": "2072", "endLine": 12, "endColumn": 50}, {"ruleId": "2069", "severity": 1, "message": "2279", "line": 12, "column": 52, "nodeType": "2071", "messageId": "2072", "endLine": 12, "endColumn": 59}, {"ruleId": "2069", "severity": 1, "message": "2284", "line": 12, "column": 61, "nodeType": "2071", "messageId": "2072", "endLine": 12, "endColumn": 71}, {"ruleId": "2069", "severity": 1, "message": "2101", "line": 6, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 6, "endColumn": 17}, {"ruleId": "2069", "severity": 1, "message": "2285", "line": 2, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 2, "endColumn": 15}, {"ruleId": "2069", "severity": 1, "message": "2286", "line": 20, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 20, "endColumn": 19}, {"ruleId": "2069", "severity": 1, "message": "2287", "line": 28, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 28, "endColumn": 24}, {"ruleId": "2069", "severity": 1, "message": "2288", "line": 36, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 36, "endColumn": 16}, {"ruleId": "2069", "severity": 1, "message": "2289", "line": 36, "column": 18, "nodeType": "2071", "messageId": "2072", "endLine": 36, "endColumn": 26}, {"ruleId": "2069", "severity": 1, "message": "2290", "line": 40, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 40, "endColumn": 32}, {"ruleId": "2069", "severity": 1, "message": "2098", "line": 48, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 48, "endColumn": 17}, {"ruleId": "2069", "severity": 1, "message": "2291", "line": 52, "column": 11, "nodeType": "2071", "messageId": "2072", "endLine": 52, "endColumn": 21}, {"ruleId": "2058", "severity": 1, "message": "2292", "line": 119, "column": 6, "nodeType": "2060", "endLine": 119, "endColumn": 29, "suggestions": "2293"}, {"ruleId": "2058", "severity": 1, "message": "2294", "line": 129, "column": 6, "nodeType": "2060", "endLine": 129, "endColumn": 23, "suggestions": "2295"}, {"ruleId": "2069", "severity": 1, "message": "2296", "line": 228, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 228, "endColumn": 24}, {"ruleId": "2069", "severity": 1, "message": "2297", "line": 228, "column": 26, "nodeType": "2071", "messageId": "2072", "endLine": 228, "endColumn": 41}, {"ruleId": "2069", "severity": 1, "message": "2298", "line": 367, "column": 13, "nodeType": "2071", "messageId": "2072", "endLine": 367, "endColumn": 16}, {"ruleId": "2069", "severity": 1, "message": "2298", "line": 380, "column": 13, "nodeType": "2071", "messageId": "2072", "endLine": 380, "endColumn": 16}, {"ruleId": "2069", "severity": 1, "message": "2299", "line": 381, "column": 13, "nodeType": "2071", "messageId": "2072", "endLine": 381, "endColumn": 20}, {"ruleId": "2069", "severity": 1, "message": "2298", "line": 398, "column": 13, "nodeType": "2071", "messageId": "2072", "endLine": 398, "endColumn": 16}, {"ruleId": "2069", "severity": 1, "message": "2299", "line": 399, "column": 13, "nodeType": "2071", "messageId": "2072", "endLine": 399, "endColumn": 20}, {"ruleId": "2069", "severity": 1, "message": "2298", "line": 414, "column": 11, "nodeType": "2071", "messageId": "2072", "endLine": 414, "endColumn": 14}, {"ruleId": "2069", "severity": 1, "message": "2300", "line": 15, "column": 20, "nodeType": "2071", "messageId": "2072", "endLine": 15, "endColumn": 26}, {"ruleId": "2069", "severity": 1, "message": "2131", "line": 15, "column": 28, "nodeType": "2071", "messageId": "2072", "endLine": 15, "endColumn": 37}, {"ruleId": "2069", "severity": 1, "message": "2301", "line": 21, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 21, "endColumn": 19}, {"ruleId": "2069", "severity": 1, "message": "2302", "line": 44, "column": 13, "nodeType": "2071", "messageId": "2072", "endLine": 44, "endColumn": 20}, {"ruleId": "2069", "severity": 1, "message": "2303", "line": 45, "column": 13, "nodeType": "2071", "messageId": "2072", "endLine": 45, "endColumn": 22}, {"ruleId": "2304", "severity": 1, "message": "2305", "line": 73, "column": 7, "nodeType": "2306", "messageId": "2122", "endLine": 73, "endColumn": 13}, {"ruleId": "2069", "severity": 1, "message": "2307", "line": 2, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 2, "endColumn": 13}, {"ruleId": "2058", "severity": 1, "message": "2308", "line": 26, "column": 8, "nodeType": "2060", "endLine": 26, "endColumn": 15, "suggestions": "2309", "suppressions": "2310"}, {"ruleId": "2069", "severity": 1, "message": "2200", "line": 7, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 7, "endColumn": 19}, {"ruleId": "2069", "severity": 1, "message": "2311", "line": 22, "column": 33, "nodeType": "2071", "messageId": "2072", "endLine": 22, "endColumn": 39}, {"ruleId": "2069", "severity": 1, "message": "2312", "line": 15, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 15, "endColumn": 14}, {"ruleId": "2069", "severity": 1, "message": "2313", "line": 15, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 15, "endColumn": 10}, {"ruleId": "2069", "severity": 1, "message": "2314", "line": 35, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 35, "endColumn": 11}, {"ruleId": "2069", "severity": 1, "message": "2315", "line": 14, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 14, "endColumn": 15}, {"ruleId": "2069", "severity": 1, "message": "2316", "line": 15, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 15, "endColumn": 16}, {"ruleId": "2058", "severity": 1, "message": "2317", "line": 40, "column": 9, "nodeType": "2060", "endLine": 40, "endColumn": 39, "suggestions": "2318", "suppressions": "2319"}, {"ruleId": "2069", "severity": 1, "message": "2320", "line": 19, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 19, "endColumn": 26}, {"ruleId": "2069", "severity": 1, "message": "2173", "line": 20, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 20, "endColumn": 13}, {"ruleId": "2069", "severity": 1, "message": "2175", "line": 20, "column": 15, "nodeType": "2071", "messageId": "2072", "endLine": 20, "endColumn": 24}, {"ruleId": "2069", "severity": 1, "message": "2176", "line": 21, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 21, "endColumn": 12}, {"ruleId": "2069", "severity": 1, "message": "2177", "line": 22, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 22, "endColumn": 21}, {"ruleId": "2069", "severity": 1, "message": "2321", "line": 4, "column": 3, "nodeType": "2071", "messageId": "2072", "endLine": 4, "endColumn": 16}, {"ruleId": "2069", "severity": 1, "message": "2322", "line": 5, "column": 3, "nodeType": "2071", "messageId": "2072", "endLine": 5, "endColumn": 16}, {"ruleId": "2069", "severity": 1, "message": "2323", "line": 6, "column": 3, "nodeType": "2071", "messageId": "2072", "endLine": 6, "endColumn": 20}, {"ruleId": "2069", "severity": 1, "message": "2172", "line": 13, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 13, "endColumn": 11}, {"ruleId": "2069", "severity": 1, "message": "2324", "line": 5, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 5, "endColumn": 12}, {"ruleId": "2069", "severity": 1, "message": "2200", "line": 6, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 6, "endColumn": 19}, {"ruleId": "2069", "severity": 1, "message": "2199", "line": 7, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 7, "endColumn": 20}, {"ruleId": "2069", "severity": 1, "message": "2325", "line": 14, "column": 46, "nodeType": "2071", "messageId": "2072", "endLine": 14, "endColumn": 52}, {"ruleId": "2069", "severity": 1, "message": "2200", "line": 7, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 7, "endColumn": 19}, {"ruleId": "2069", "severity": 1, "message": "2326", "line": 15, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 15, "endColumn": 14}, {"ruleId": "2069", "severity": 1, "message": "2327", "line": 20, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 20, "endColumn": 20}, {"ruleId": "2069", "severity": 1, "message": "2328", "line": 23, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 23, "endColumn": 26}, {"ruleId": "2069", "severity": 1, "message": "2172", "line": 24, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 24, "endColumn": 11}, {"ruleId": "2069", "severity": 1, "message": "2329", "line": 26, "column": 27, "nodeType": "2071", "messageId": "2072", "endLine": 26, "endColumn": 34}, {"ruleId": "2069", "severity": 1, "message": "2141", "line": 110, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 110, "endColumn": 17}, {"ruleId": "2069", "severity": 1, "message": "2330", "line": 15, "column": 63, "nodeType": "2071", "messageId": "2072", "endLine": 15, "endColumn": 72}, {"ruleId": "2069", "severity": 1, "message": "2331", "line": 16, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 16, "endColumn": 23}, {"ruleId": "2069", "severity": 1, "message": "2332", "line": 16, "column": 25, "nodeType": "2071", "messageId": "2072", "endLine": 16, "endColumn": 35}, {"ruleId": "2069", "severity": 1, "message": "2333", "line": 80, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 80, "endColumn": 25}, {"ruleId": "2069", "severity": 1, "message": "2334", "line": 81, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 81, "endColumn": 22}, {"ruleId": "2069", "severity": 1, "message": "2335", "line": 82, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 82, "endColumn": 16}, {"ruleId": "2069", "severity": 1, "message": "2336", "line": 82, "column": 18, "nodeType": "2071", "messageId": "2072", "endLine": 82, "endColumn": 27}, {"ruleId": "2069", "severity": 1, "message": "2337", "line": 84, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 84, "endColumn": 28}, {"ruleId": "2069", "severity": 1, "message": "2338", "line": 94, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 94, "endColumn": 28}, {"ruleId": "2069", "severity": 1, "message": "2339", "line": 97, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 97, "endColumn": 16}, {"ruleId": "2069", "severity": 1, "message": "2340", "line": 3, "column": 36, "nodeType": "2071", "messageId": "2072", "endLine": 3, "endColumn": 43}, {"ruleId": "2069", "severity": 1, "message": "2277", "line": 20, "column": 23, "nodeType": "2071", "messageId": "2072", "endLine": 20, "endColumn": 34}, {"ruleId": "2069", "severity": 1, "message": "2102", "line": 45, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 45, "endColumn": 18}, {"ruleId": "2058", "severity": 1, "message": "2341", "line": 313, "column": 6, "nodeType": "2060", "endLine": 313, "endColumn": 17, "suggestions": "2342"}, {"ruleId": "2058", "severity": 1, "message": "2343", "line": 78, "column": 6, "nodeType": "2060", "endLine": 78, "endColumn": 8, "suggestions": "2344", "suppressions": "2345"}, {"ruleId": "2069", "severity": 1, "message": "2173", "line": 3, "column": 22, "nodeType": "2071", "messageId": "2072", "endLine": 3, "endColumn": 25}, {"ruleId": "2069", "severity": 1, "message": "2174", "line": 3, "column": 52, "nodeType": "2071", "messageId": "2072", "endLine": 3, "endColumn": 58}, {"ruleId": "2058", "severity": 1, "message": "2346", "line": 108, "column": 6, "nodeType": "2060", "endLine": 108, "endColumn": 17, "suggestions": "2347"}, {"ruleId": "2069", "severity": 1, "message": "2108", "line": 114, "column": 5, "nodeType": "2071", "messageId": "2072", "endLine": 114, "endColumn": 10}, {"ruleId": "2069", "severity": 1, "message": "2348", "line": 115, "column": 5, "nodeType": "2071", "messageId": "2072", "endLine": 115, "endColumn": 13}, {"ruleId": "2069", "severity": 1, "message": "2349", "line": 165, "column": 13, "nodeType": "2071", "messageId": "2072", "endLine": 165, "endColumn": 19}, {"ruleId": "2069", "severity": 1, "message": "2350", "line": 4, "column": 15, "nodeType": "2071", "messageId": "2072", "endLine": 4, "endColumn": 31}, {"ruleId": "2069", "severity": 1, "message": "2351", "line": 5, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 5, "endColumn": 21}, {"ruleId": "2069", "severity": 1, "message": "2352", "line": 18, "column": 28, "nodeType": "2071", "messageId": "2072", "endLine": 18, "endColumn": 51}, {"ruleId": "2069", "severity": 1, "message": "2353", "line": 22, "column": 15, "nodeType": "2071", "messageId": "2072", "endLine": 22, "endColumn": 27}, {"ruleId": "2069", "severity": 1, "message": "2354", "line": 23, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 23, "endColumn": 14}, {"ruleId": "2069", "severity": 1, "message": "2355", "line": 31, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 31, "endColumn": 12}, {"ruleId": "2069", "severity": 1, "message": "2356", "line": 88, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 88, "endColumn": 22}, {"ruleId": "2069", "severity": 1, "message": "2357", "line": 95, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 95, "endColumn": 18}, {"ruleId": "2069", "severity": 1, "message": "2358", "line": 167, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 167, "endColumn": 18}, {"ruleId": "2069", "severity": 1, "message": "2359", "line": 168, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 168, "endColumn": 14}, {"ruleId": "2069", "severity": 1, "message": "2360", "line": 169, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 169, "endColumn": 28}, {"ruleId": "2069", "severity": 1, "message": "2361", "line": 170, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 170, "endColumn": 22}, {"ruleId": "2069", "severity": 1, "message": "2362", "line": 180, "column": 12, "nodeType": "2071", "messageId": "2072", "endLine": 180, "endColumn": 24}, {"ruleId": "2069", "severity": 1, "message": "2099", "line": 27, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 27, "endColumn": 13}, {"ruleId": "2069", "severity": 1, "message": "2363", "line": 165, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 165, "endColumn": 18}, {"ruleId": "2058", "severity": 1, "message": "2248", "line": 421, "column": 6, "nodeType": "2060", "endLine": 421, "endColumn": 22, "suggestions": "2364"}, {"ruleId": "2123", "severity": 1, "message": "2124", "line": 768, "column": 23, "nodeType": "2125", "endLine": 768, "endColumn": 41}, {"ruleId": "2069", "severity": 1, "message": "2365", "line": 78, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 78, "endColumn": 22}, {"ruleId": "2069", "severity": 1, "message": "2366", "line": 78, "column": 24, "nodeType": "2071", "messageId": "2072", "endLine": 78, "endColumn": 39}, {"ruleId": "2058", "severity": 1, "message": "2248", "line": 222, "column": 6, "nodeType": "2060", "endLine": 222, "endColumn": 22, "suggestions": "2367"}, {"ruleId": "2069", "severity": 1, "message": "2114", "line": 37, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 37, "endColumn": 17}, {"ruleId": "2058", "severity": 1, "message": "2368", "line": 302, "column": 6, "nodeType": "2060", "endLine": 302, "endColumn": 8, "suggestions": "2369"}, {"ruleId": "2058", "severity": 1, "message": "2248", "line": 156, "column": 6, "nodeType": "2060", "endLine": 156, "endColumn": 22, "suggestions": "2370"}, {"ruleId": "2069", "severity": 1, "message": "2131", "line": 1, "column": 18, "nodeType": "2071", "messageId": "2072", "endLine": 1, "endColumn": 27}, {"ruleId": "2069", "severity": 1, "message": "2141", "line": 20, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 20, "endColumn": 17}, {"ruleId": "2058", "severity": 1, "message": "2371", "line": 28, "column": 9, "nodeType": "2372", "endLine": 60, "endColumn": 13}, {"ruleId": "2069", "severity": 1, "message": "2202", "line": 12, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 12, "endColumn": 16}, {"ruleId": "2069", "severity": 1, "message": "2373", "line": 39, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 39, "endColumn": 18}, {"ruleId": "2069", "severity": 1, "message": "2374", "line": 18, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 18, "endColumn": 27}, {"ruleId": "2069", "severity": 1, "message": "2114", "line": 87, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 87, "endColumn": 17}, {"ruleId": "2069", "severity": 1, "message": "2358", "line": 139, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 139, "endColumn": 18}, {"ruleId": "2069", "severity": 1, "message": "2359", "line": 140, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 140, "endColumn": 14}, {"ruleId": "2069", "severity": 1, "message": "2360", "line": 141, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 141, "endColumn": 28}, {"ruleId": "2069", "severity": 1, "message": "2361", "line": 142, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 142, "endColumn": 22}, {"ruleId": "2069", "severity": 1, "message": "2375", "line": 266, "column": 11, "nodeType": "2071", "messageId": "2072", "endLine": 266, "endColumn": 20}, {"ruleId": "2069", "severity": 1, "message": "2376", "line": 267, "column": 11, "nodeType": "2071", "messageId": "2072", "endLine": 267, "endColumn": 19}, {"ruleId": "2069", "severity": 1, "message": "2377", "line": 471, "column": 17, "nodeType": "2071", "messageId": "2072", "endLine": 471, "endColumn": 28}, {"ruleId": "2069", "severity": 1, "message": "2291", "line": 472, "column": 20, "nodeType": "2071", "messageId": "2072", "endLine": 472, "endColumn": 30}, {"ruleId": "2069", "severity": 1, "message": "2378", "line": 472, "column": 32, "nodeType": "2071", "messageId": "2072", "endLine": 472, "endColumn": 38}, {"ruleId": "2069", "severity": 1, "message": "2379", "line": 485, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 485, "endColumn": 32}, {"ruleId": "2069", "severity": 1, "message": "2350", "line": 2, "column": 23, "nodeType": "2071", "messageId": "2072", "endLine": 2, "endColumn": 39}, {"ruleId": "2069", "severity": 1, "message": "2380", "line": 2, "column": 41, "nodeType": "2071", "messageId": "2072", "endLine": 2, "endColumn": 48}, {"ruleId": "2069", "severity": 1, "message": "2261", "line": 3, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 3, "endColumn": 21}, {"ruleId": "2069", "severity": 1, "message": "2381", "line": 3, "column": 23, "nodeType": "2071", "messageId": "2072", "endLine": 3, "endColumn": 29}, {"ruleId": "2069", "severity": 1, "message": "2326", "line": 7, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 7, "endColumn": 14}, {"ruleId": "2069", "severity": 1, "message": "2382", "line": 78, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 78, "endColumn": 20}, {"ruleId": "2069", "severity": 1, "message": "2383", "line": 81, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 81, "endColumn": 20}, {"ruleId": "2069", "severity": 1, "message": "2384", "line": 100, "column": 9, "nodeType": "2071", "messageId": "2072", "endLine": 100, "endColumn": 29}, {"ruleId": "2069", "severity": 1, "message": "2385", "line": 5, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 5, "endColumn": 18}, {"ruleId": "2069", "severity": 1, "message": "2340", "line": 7, "column": 3, "nodeType": "2071", "messageId": "2072", "endLine": 7, "endColumn": 10}, {"ruleId": "2069", "severity": 1, "message": "2386", "line": 11, "column": 3, "nodeType": "2071", "messageId": "2072", "endLine": 11, "endColumn": 14}, {"ruleId": "2069", "severity": 1, "message": "2387", "line": 12, "column": 3, "nodeType": "2071", "messageId": "2072", "endLine": 12, "endColumn": 12}, {"ruleId": "2069", "severity": 1, "message": "2388", "line": 69, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 69, "endColumn": 15}, {"ruleId": "2069", "severity": 1, "message": "2389", "line": 199, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 199, "endColumn": 18}, {"ruleId": "2069", "severity": 1, "message": "2390", "line": 288, "column": 7, "nodeType": "2071", "messageId": "2072", "endLine": 288, "endColumn": 21}, {"ruleId": "2069", "severity": 1, "message": "2391", "line": 5, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 5, "endColumn": 14}, {"ruleId": "2069", "severity": 1, "message": "2265", "line": 6, "column": 8, "nodeType": "2071", "messageId": "2072", "endLine": 6, "endColumn": 13}, {"ruleId": "2058", "severity": 1, "message": "2392", "line": 244, "column": 6, "nodeType": "2060", "endLine": 244, "endColumn": 16, "suggestions": "2393"}, {"ruleId": "2069", "severity": 1, "message": "2261", "line": 17, "column": 50, "nodeType": "2071", "messageId": "2072", "endLine": 17, "endColumn": 61}, {"ruleId": "2069", "severity": 1, "message": "2207", "line": 22, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 22, "endColumn": 19}, {"ruleId": "2069", "severity": 1, "message": "2394", "line": 151, "column": 10, "nodeType": "2071", "messageId": "2072", "endLine": 151, "endColumn": 21}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'page'. Either include it or remove the dependency array.", "ArrayExpression", ["2395"], ["2396"], "React Hook useEffect has missing dependencies: 'isContrastMode' and 'page.classList'. Either include them or remove the dependency array.", ["2397"], ["2398"], "React Hook useEffect has missing dependencies: 'page.dataset', 'page.style', 'savePreferences', and 'stopTransition'. Either include them or remove the dependency array. Outer scope values like 'window.devicePixelRatio' aren't valid dependencies because mutating them doesn't re-render the component.", ["2399"], ["2400"], "no-unused-vars", "'messaging' is defined but never used.", "Identifier", "unusedVar", "'getNotificationsOfAdmin' is defined but never used.", "'getNotificationsOfNurse' is defined but never used.", "'onMessage' is defined but never used.", "'DashboardA' is assigned a value but never used.", "'DashboardB' is assigned a value but never used.", "'DashboardC' is assigned a value but never used.", "'DashboardD' is assigned a value but never used.", "'DashboardE' is assigned a value but never used.", "'DashboardF' is assigned a value but never used.", "'DashboardG' is assigned a value but never used.", "'DashboardH' is assigned a value but never used.", "'DashboardI' is assigned a value but never used.", "'DashboardJ' is assigned a value but never used.", "'DashboardK' is assigned a value but never used.", "'DoctorAppointments' is assigned a value but never used.", "'Tests' is assigned a value but never used.", "'DoctorsReviews' is assigned a value but never used.", "'PatientReviews' is assigned a value but never used.", "'Finances' is assigned a value but never used.", "'PageNotFound' is assigned a value but never used.", "'AddCaregiver' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", ["2401"], "'getDoc' is defined but never used.", "'rows' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "'Phone' is defined but never used.", "'PhoneNumberInput' is defined but never used.", "'PropTypes' is defined but never used.", "'StyledField' is assigned a value but never used.", "'GENDER_SELECT' is assigned a value but never used.", "'handleSubmit' is assigned a value but never used.", "'control' is assigned a value but never used.", "'errors' is assigned a value but never used.", "'isSubmitting' is assigned a value but never used.", "'watch' is assigned a value but never used.", "'onSelectImage' is defined but never used.", "'submitForm' is defined but never used.", "React Hook useEffect has missing dependencies: 'caregiver_id', 'caregivers', and 'setValue'. Either include them or remove the dependency array.", ["2402"], "'FormControl' is defined but never used.", "'DocPreview' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'current_user', 'setValue', and 'user_id'. Either include them or remove the dependency array.", ["2403"], "React Hook useEffect has missing dependencies: 'current_user?.role', 'current_user?.type', and 'setValue'. Either include them or remove the dependency array.", ["2404"], "no-empty-pattern", "Unexpected empty object pattern.", "ObjectPattern", "unexpected", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "JSXOpeningElement", "'app' is defined but never used.", "'getMessaging' is defined but never used.", "React Hook useEffect has a missing dependency: 'chat_id'. Either include it or remove the dependency array.", ["2405"], "'AdminPanelSettings' is defined but never used.", "'useEffect' is defined but never used.", "'IconButton' is defined but never used.", "'ArrowBack' is defined but never used.", "'TaskListIcon' is defined but never used.", "'CalendarIcon' is defined but never used.", "'isRecurringAppointment' is defined but never used.", "'Mood' is defined but never used.", "'mobility' is defined but never used.", "'appitete' is defined but never used.", "'RedShifInfoCard' is assigned a value but never used.", "'dispatch' is assigned a value but never used.", "'nextAppointment' is assigned a value but never used.", "'seriesInfo' is assigned a value but never used.", "'handleBackToCalendar' is assigned a value but never used.", "'DailyAppointmentChart' is defined but never used.", "'PatientOverallAppointments' is defined but never used.", "'PatientsRadialBar' is defined but never used.", "'ConfirmedDiagnoses' is defined but never used.", "'DailyAppointmentsByDoc' is defined but never used.", "'DiagnosesDonut' is defined but never used.", "'PatientAppointmentsHistory' is defined but never used.", "'RadarAreaChart' is defined but never used.", "'useWindowSize' is defined but never used.", "'useState' is defined but never used.", "'nanoid' is defined but never used.", "React Hook useEffect has missing dependencies: 'currentMonth', 'scrollToTop', 'setDayIndex', 'setMonthIndex', and 'todayIndex'. Either include them or remove the dependency array.", ["2406"], ["2407"], "React Hook useEffect has a missing dependency: 'scrollToTop'. Either include it or remove the dependency array.", ["2408"], ["2409"], "'ContrastControl' is defined but never used.", "'LayoutControl' is defined but never used.", "'ScaleControl' is defined but never used.", "'DirectionControl' is defined but never used.", "'isDashboard' is assigned a value but never used.", "'CustomSelect' is defined but never used.", "'depsOptions' is defined but never used.", "'Item' is defined but never used.", "'setCategory' is assigned a value but never used.", "'TodosLegend' is defined but never used.", "'Btn' is defined but never used.", "'doc' is defined but never used.", "'setDoc' is defined but never used.", "'updateDoc' is defined but never used.", "'db' is defined but never used.", "'COLLECTIONS' is defined but never used.", "'addNewTaskDocAction' is defined but never used.", "'todos' is assigned a value but never used.", "'currentPatient' is assigned a value but never used.", "'currentCaregiver' is assigned a value but never used.", "'currentNurse' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "'enqueueSnackbar' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'changeHandler', 'options', 'placeholder', and 'value'. Either include them or remove the dependency array. If 'changeHandler' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2410"], ["2411"], "'SelectPlaceholder' is defined but never used.", "'doctor' is defined but never used.", "'patient' is defined but never used.", "'Box' is defined but never used.", "'WidgetsLoader' is defined but never used.", "'setChatsAction' is defined but never used.", "'generateNames' is defined but never used.", "'drawList' is assigned a value but never used.", ["2412"], "'Reminder' is defined but never used.", "'ActionButton' is defined but never used.", "'ShapeButton' is defined but never used.", "'styled' is defined but never used.", "'colors' is defined but never used.", "'dark' is defined but never used.", "'flex' is defined but never used.", "'fonts' is defined but never used.", "'light' is defined but never used.", "'textSizes' is defined but never used.", "'theme' is defined but never used.", "'AppointmentChip' is defined but never used.", "'caregivers' is defined but never used.", "'placeholder' is defined but never used.", "'MonthNavigator' is defined but never used.", "'setMonth' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setIndex'. Either include it or remove the dependency array.", ["2413"], ["2414"], ["2415"], ["2416"], "React Hook useEffect has missing dependencies: 'currentMonth' and 'setIndex'. Either include them or remove the dependency array.", ["2417"], ["2418"], "React Hook useEffect has a missing dependency: 'data'. Either include it or remove the dependency array.", ["2419"], ["2420"], "React Hook useLayoutEffect has a missing dependency: 'page'. Either include it or remove the dependency array.", ["2421"], ["2422"], "'RangePickerWrapper' is defined but never used.", "'AdapterMoment' is defined but never used.", "'LocalizationProvider' is defined but never used.", "'DatePicker' is defined but never used.", "'Emergency' is defined but never used.", "'QtyBadge' is defined but never used.", "'unreadCount' is assigned a value but never used.", "'Input' is defined but never used.", "'Label' is defined but never used.", "'Search' is defined but never used.", "'user' is assigned a value but never used.", "'handleClick' is assigned a value but never used.", "'appointmentDate' is assigned a value but never used.", "'today' is assigned a value but never used.", "'Timestamp' is defined but never used.", "'startDateTime' is assigned a value but never used.", "'endDateTime' is assigned a value but never used.", "'register' is assigned a value but never used.", "'recurrenceType' is assigned a value but never used.", "'recurrenceEndDate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setValue'. Either include it or remove the dependency array.", ["2423"], "React Hook useEffect has missing dependencies: 'setValue' and 'watch'. Either include them or remove the dependency array.", ["2424"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "CallExpression", ["2425"], "React Hook useEffect has missing dependencies: 'defaultValues?.caregiver', 'isFromCalendar', and 'setValue'. Either include them or remove the dependency array.", ["2426"], ["2427"], "'CustomRating' is defined but never used.", "'EditBtn' is defined but never used.", "'ChatBtn' is defined but never used.", "'CheckCircle' is defined but never used.", "'BlockIcon' is defined but never used.", "'Info' is assigned a value but never used.", "'InputWrapper' is defined but never used.", "'Field' is defined but never used.", "'addTodo' is defined but never used.", "'toggleCollapse' is defined but never used.", "'tasksOptions' is defined but never used.", "'addDoc' is defined but never used.", "'collection' is defined but never used.", "'addNewTaskToList' is defined but never used.", "'createNewTaskDocThunk' is defined but never used.", "'setTaskDetailsAction' is defined but never used.", "'Button' is defined but never used.", "'Add' is defined but never used.", "'tasksDetails' is assigned a value but never used.", "'useSelector' is defined but never used.", "'device' is assigned a value but never used.", "'getDocs' is defined but never used.", "React Hook useEffect has missing dependencies: 'activeChat' and 'current_chat'. Either include them or remove the dependency array.", ["2428"], "'query' is defined but never used.", "'where' is defined but never used.", "'writeBatch' is defined but never used.", "'Badge' is defined but never used.", "'DoctorPopup' is defined but never used.", "'doctorsOptions' is defined but never used.", "'events' is defined but never used.", "'disabled' is defined but never used.", "'ScheduleAppointmentModal' is defined but never used.", "'caregivers' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'patientOptions' and 'selectedPatient'. Either include them or remove the dependency array.", ["2429"], "React Hook useEffect has missing dependencies: 'searchParams' and 'setSearchParams'. Either include them or remove the dependency array.", ["2430"], "'isScheduleOpen' is assigned a value but never used.", "'setScheduleOpen' is assigned a value but never used.", "'now' is assigned a value but never used.", "'current' is assigned a value but never used.", "'useRef' is defined but never used.", "'popupOpen' is assigned a value but never used.", "'endDate' is assigned a value but never used.", "'startDate' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'MISSED'.", "ObjectExpression", "'Nav' is defined but never used.", "React Hook useLayoutEffect has a missing dependency: 'id'. Either include it or remove the dependency array. If 'setPoints' needs the current value of 'id', you can also switch to useReducer instead of useState and read 'id' in the reducer.", ["2431"], ["2432"], "'doctor' is assigned a value but never used.", "'menu' is defined but never used.", "'Img' is assigned a value but never used.", "'Text' is assigned a value but never used.", "'doc1jpg' is defined but never used.", "'doc1webp' is defined but never used.", "React Hook useEffect has a missing dependency: 'handleScroll'. Either include it or remove the dependency array.", ["2433"], ["2434"], "'updateTaskAction' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogActions' is defined but never used.", "'DialogContentText' is defined but never used.", "'pen' is defined but never used.", "'avatar' is assigned a value but never used.", "'moment' is defined but never used.", "'addMessage' is defined but never used.", "'updateChatAction' is defined but never used.", "'Popover' is defined but never used.", "'TextField' is defined but never used.", "'VisibilityOff' is defined but never used.", "'Visibility' is defined but never used.", "'selectedCountry' is assigned a value but never used.", "'selectedCity' is assigned a value but never used.", "'cities' is assigned a value but never used.", "'setCities' is assigned a value but never used.", "'getCountriesOptions' is assigned a value but never used.", "'handleCountryChange' is assigned a value but never used.", "'options' is assigned a value but never used.", "'Divider' is defined but never used.", "React Hook useEffect has missing dependencies: 'defaultValues?.caregiver', 'defaultValues?.date', 'isFromCalendar', and 'setValue'. Either include them or remove the dependency array.", ["2435"], "React Hook useEffect has a missing dependency: 'init'. Either include it or remove the dependency array.", ["2436"], ["2437"], "React Hook useEffect has missing dependencies: 'dispatch', 'notifications', and 'user?.role'. Either include them or remove the dependency array.", ["2438"], "'setValue' is assigned a value but never used.", "'docRef' is assigned a value but never used.", "'CircularProgress' is defined but never used.", "'breakpoints' is defined but never used.", "'AppointmentsHeadingIcon' is defined but never used.", "'TabContainer' is defined but never used.", "'TabNav' is defined but never used.", "'Label' is assigned a value but never used.", "'ReasonOfRequest' is assigned a value but never used.", "'GrayedInput' is assigned a value but never used.", "'caregiver' is assigned a value but never used.", "'nurse' is assigned a value but never used.", "'client_appointments' is assigned a value but never used.", "'findCaregiver' is assigned a value but never used.", "'renderGender' is defined but never used.", "'client_id' is assigned a value but never used.", ["2439"], "'searchParams' is assigned a value but never used.", "'setSearchParams' is assigned a value but never used.", ["2440"], "React Hook useEffect has missing dependencies: 'currentPatient?.bloodPressure', 'currentPatient?.bloodSugar', 'currentPatient?.id', 'currentPatient?.medAdminRequired', 'currentPatient?.needLabs', 'currentPatient?.pulse', 'currentPatient?.recommendedServices', 'currentPatient?.spo2', 'currentPatient?.temperature', 'currentPatient?.wounds', and 'setValue'. Either include them or remove the dependency array.", ["2441"], ["2442"], "The 'upcomingAppointments' logical expression could make the dependencies of useMemo Hook (at line 74) change on every render. Move it inside the useMemo callback. Alternatively, wrap the initialization of 'upcomingAppointments' in its own useMemo() Hook.", "VariableDeclarator", "'StaffStatus' is assigned a value but never used.", "'AppointmentListItem' is defined but never used.", "'firstName' is assigned a value but never used.", "'lastName' is assigned a value but never used.", "'logged_user' is assigned a value but never used.", "'nurses' is assigned a value but never used.", "'sortedStaffAppointments' is assigned a value but never used.", "'Tooltip' is defined but never used.", "'Cancel' is defined but never used.", "'canComplete' is assigned a value but never used.", "'tooltipText' is assigned a value but never used.", "'handleAddAppointment' is assigned a value but never used.", "'borderColor' is assigned a value but never used.", "'CardContent' is defined but never used.", "'CardMedia' is defined but never used.", "'InfoCard' is assigned a value but never used.", "'ServiceLeft' is assigned a value but never used.", "'SignatureTitle' is assigned a value but never used.", "'Grid' is defined but never used.", "React Hook useEffect has missing dependencies: 'addressComponents.addressLine2' and 'onChange'. Either include them or remove the dependency array. If 'onChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2443"], "'copiedField' is assigned a value but never used.", {"desc": "2444", "fix": "2445"}, {"kind": "2446", "justification": "2447"}, {"desc": "2448", "fix": "2449"}, {"kind": "2446", "justification": "2447"}, {"desc": "2450", "fix": "2451"}, {"kind": "2446", "justification": "2447"}, {"desc": "2452", "fix": "2453"}, {"desc": "2454", "fix": "2455"}, {"desc": "2456", "fix": "2457"}, {"desc": "2458", "fix": "2459"}, {"desc": "2460", "fix": "2461"}, {"desc": "2462", "fix": "2463"}, {"kind": "2446", "justification": "2447"}, {"desc": "2464", "fix": "2465"}, {"kind": "2446", "justification": "2447"}, {"desc": "2466", "fix": "2467"}, {"kind": "2446", "justification": "2447"}, {"desc": "2468", "fix": "2469"}, {"desc": "2470", "fix": "2471"}, {"kind": "2446", "justification": "2447"}, {"desc": "2470", "fix": "2472"}, {"kind": "2446", "justification": "2447"}, {"desc": "2473", "fix": "2474"}, {"kind": "2446", "justification": "2447"}, {"desc": "2475", "fix": "2476"}, {"kind": "2446", "justification": "2447"}, {"desc": "2477", "fix": "2478"}, {"kind": "2446", "justification": "2447"}, {"desc": "2479", "fix": "2480"}, {"desc": "2481", "fix": "2482"}, {"desc": "2481", "fix": "2483"}, {"desc": "2484", "fix": "2485"}, {"desc": "2481", "fix": "2486"}, {"desc": "2487", "fix": "2488"}, {"desc": "2489", "fix": "2490"}, {"desc": "2491", "fix": "2492"}, {"desc": "2493", "fix": "2494"}, {"kind": "2446", "justification": "2447"}, {"desc": "2495", "fix": "2496"}, {"kind": "2446", "justification": "2447"}, {"desc": "2497", "fix": "2498"}, {"desc": "2499", "fix": "2500"}, {"kind": "2446", "justification": "2447"}, {"desc": "2501", "fix": "2502"}, {"desc": "2503", "fix": "2504"}, {"desc": "2503", "fix": "2505"}, {"desc": "2506", "fix": "2507"}, {"desc": "2503", "fix": "2508"}, {"desc": "2509", "fix": "2510"}, "Update the dependencies array to be: [direction, page]", {"range": "2511", "text": "2512"}, "directive", "", "Update the dependencies array to be: [isContrastMode, page.classList]", {"range": "2513", "text": "2514"}, "Update the dependencies array to be: [isDarkMode, isContrastMode, fontScale, direction, page.style, page.dataset, savePreferences, stopTransition]", {"range": "2515", "text": "2516"}, "Update the dependencies array to be: [dispatch, user]", {"range": "2517", "text": "2518"}, "Update the dependencies array to be: [caregiver_id, caregivers, caregivers.length, setValue]", {"range": "2519", "text": "2520"}, "Update the dependencies array to be: [all_users.length, current_user, setValue, user_id]", {"range": "2521", "text": "2522"}, "Update the dependencies array to be: [activeTab, current_user?.role, current_user?.type, setValue]", {"range": "2523", "text": "2524"}, "Update the dependencies array to be: [activeList, chat_id]", {"range": "2525", "text": "2526"}, "Update the dependencies array to be: [currentMonth, scrollToTop, selectedTab, setDayIndex, setMonthIndex, todayIndex]", {"range": "2527", "text": "2528"}, "Update the dependencies array to be: [dayIndex, monthIndex, scrollToTop]", {"range": "2529", "text": "2530"}, "Update the dependencies array to be: [changeHandler, options, placeholder, value]", {"range": "2531", "text": "2532"}, "Update the dependencies array to be: [dispatch, logged_in_user?.id]", {"range": "2533", "text": "2534"}, "Update the dependencies array to be: [setIndex, todayIndex]", {"range": "2535", "text": "2536"}, {"range": "2537", "text": "2536"}, "Update the dependencies array to be: [currentMonth, setIndex]", {"range": "2538", "text": "2539"}, "Update the dependencies array to be: [data, index]", {"range": "2540", "text": "2541"}, "Update the dependencies array to be: [page]", {"range": "2542", "text": "2543"}, "Update the dependencies array to be: [date, setValue]", {"range": "2544", "text": "2545"}, "Update the dependencies array to be: [setValue, watch]", {"range": "2546", "text": "2547"}, {"range": "2548", "text": "2547"}, "Update the dependencies array to be: [defaultValues?.caregiver, isFromCalendar, open, setValue]", {"range": "2549", "text": "2550"}, {"range": "2551", "text": "2547"}, "Update the dependencies array to be: [activeChat, current_chat, user]", {"range": "2552", "text": "2553"}, "Update the dependencies array to be: [clients, patientOptions, searchParams, selectedPatient]", {"range": "2554", "text": "2555"}, "Update the dependencies array to be: [searchParams, selectedPatient, setSearchParams]", {"range": "2556", "text": "2557"}, "Update the dependencies array to be: [id, width]", {"range": "2558", "text": "2559"}, "Update the dependencies array to be: [ref, callback, contentHeight, handleScroll]", {"range": "2560", "text": "2561"}, "Update the dependencies array to be: [defaultValues?.caregiver, defaultValues?.date, isFromCalendar, isVisible, setValue]", {"range": "2562", "text": "2563"}, "Update the dependencies array to be: [init]", {"range": "2564", "text": "2565"}, "Update the dependencies array to be: [dispatch, notifications, user?.id, user?.role]", {"range": "2566", "text": "2567"}, "Update the dependencies array to be: [currentPatient, setValue]", {"range": "2568", "text": "2569"}, {"range": "2570", "text": "2569"}, "Update the dependencies array to be: [currentPatient?.bloodPressure, currentPatient?.bloodSugar, currentPatient?.id, currentPatient?.medAdminRequired, currentPatient?.needLabs, currentPatient?.pulse, currentPatient?.recommendedServices, currentPatient?.spo2, currentPatient?.temperature, currentPatient?.wounds, setValue]", {"range": "2571", "text": "2572"}, {"range": "2573", "text": "2569"}, "Update the dependencies array to be: [addressComponents.addressLine2, onChange, placeVal]", {"range": "2574", "text": "2575"}, [1425, 1436], "[direction, page]", [1607, 1609], "[isContrastMode, page.classList]", [2304, 2379], "[isDarkMode, isContrastMode, fontScale, direction, page.style, page.dataset, savePreferences, stopTransition]", [4670, 4676], "[dispatch, user]", [4933, 4953], "[caregiver_id, caregivers, caregivers.length, setValue]", [19640, 19659], "[all_users.length, current_user, setValue, user_id]", [19874, 19885], "[activeTab, current_user?.role, current_user?.type, setValue]", [1407, 1419], "[activeList, chat_id]", [2768, 2781], "[current<PERSON><PERSON><PERSON>, scrollT<PERSON><PERSON><PERSON>, selectedTab, setDayIndex, setMonthIndex, todayIndex]", [2905, 2927], "[dayIndex, monthIndex, scrollToTop]", [618, 620], "[changeHandler, options, placeholder, value]", [3482, 3502], "[dispatch, logged_in_user?.id]", [2037, 2049], "[setIndex, todayIndex]", [1416, 1428], [1136, 1138], "[current<PERSON><PERSON><PERSON>, setIndex]", [1480, 1487], "[data, index]", [590, 592], "[page]", [26482, 26488], "[date, setValue]", [27336, 27360], "[set<PERSON><PERSON><PERSON>, watch]", [27505, 27535], [27716, 27722], "[defaultValues?.caregiver, isFromCalendar, open, setValue]", [28983, 29071], [2276, 2282], "[activeChat, current_chat, user]", [4565, 4588], "[clients, patientOptions, searchParams, selectedPatient]", [4928, 4945], "[searchPara<PERSON>, selectedPatient, setSearchParams]", [882, 889], "[id, width]", [1578, 1608], "[ref, callback, contentHeight, handleScroll]", [12543, 12554], "[defaultValues?.caregiver, defaultValues?.date, isFromCalendar, isVisible, setValue]", [1927, 1929], "[init]", [3467, 3478], "[dispatch, notifications, user?.id, user?.role]", [14400, 14416], "[current<PERSON><PERSON><PERSON>, setValue]", [7722, 7738], [10147, 10149], "[currentPatient?.bloodPressure, currentPatient?.bloodSugar, currentPatient?.id, currentPatient?.medAdminRequired, currentPatient?.needLabs, currentPatient?.pulse, currentPatient?.recommendedServices, currentPatient?.spo2, currentPatient?.temperature, currentPatient?.wounds, setValue]", [5785, 5801], [6144, 6154], "[addressComponents.addressLine2, onChange, placeVal]"]