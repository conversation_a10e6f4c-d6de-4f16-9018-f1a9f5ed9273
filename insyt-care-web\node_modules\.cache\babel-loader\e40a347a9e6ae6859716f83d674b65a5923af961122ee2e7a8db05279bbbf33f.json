{"ast": null, "code": "var _jsxFileName = \"D:\\\\Softwares\\\\insyt-care\\\\insyt-care-web\\\\src\\\\layout\\\\ProtectedRoutes\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport usePageIsOverflow from \"@hooks/usePageIsOverflow\";\nimport useWindowSize from \"@hooks/useWindowSize\";\nimport Panel from \"@layout/Panel\";\nimport Sidebar from \"@layout/Sidebar\";\nimport ScrollProgress from \"@ui/ScrollProgress\";\nimport React, { useEffect, useRef, useState } from \"react\";\nimport { useSelector } from \"react-redux\";\nimport { Navigate, Outlet } from \"react-router-dom\";\nimport { ThemeProvider as MuiThemeProvider } from \"@mui/material/styles\";\nimport { theme } from \"@styles/mui-theme\";\nimport { Suspense } from \"react\";\nimport WidgetsLoader from \"@components/WidgetsLoader\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProtectedRoutes = () => {\n  _s();\n  const appRef = useRef(null);\n  const isOverflow = usePageIsOverflow();\n  const userId = localStorage.getItem(\"userId\");\n  const {\n    user,\n    isLoading\n  } = useSelector(state => state.auth);\n  useEffect(() => {\n    if (appRef.current) {\n      appRef.current.scrollTo(0, 0);\n    }\n  }, []);\n\n  // If we have userId but no user data and not loading, redirect to login\n  if (userId && !user && !isLoading) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 12\n    }, this);\n  }\n\n  // If we have userId and user data, show protected content\n  if (userId && user) {\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: /*#__PURE__*/_jsxDEV(MuiThemeProvider, {\n        theme: theme,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"app\",\n          ref: appRef,\n          children: [isOverflow ? /*#__PURE__*/_jsxDEV(ScrollProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 27\n          }, this) : null, /*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"app_content\",\n            children: [/*#__PURE__*/_jsxDEV(Panel, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Suspense, {\n              fallback: /*#__PURE__*/_jsxDEV(WidgetsLoader, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 35\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this)\n    }, void 0, false);\n  }\n\n  // If we have userId and still loading, show loading state\n  if (userId && isLoading) {\n    return /*#__PURE__*/_jsxDEV(WidgetsLoader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Default case: no userId or authentication failed, redirect to login\n  return /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/login\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 10\n  }, this);\n};\n_s(ProtectedRoutes, \"MNoeR0w94b4KF+HkLcTuKIwNXEM=\", false, function () {\n  return [usePageIsOverflow, useSelector];\n});\n_c = ProtectedRoutes;\nexport default ProtectedRoutes;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoutes\");", "map": {"version": 3, "names": ["usePageIsOverflow", "useWindowSize", "Panel", "Sidebar", "ScrollProgress", "React", "useEffect", "useRef", "useState", "useSelector", "Navigate", "Outlet", "ThemeProvider", "MuiThemeProvider", "theme", "Suspense", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProtectedRoutes", "_s", "appRef", "isOverflow", "userId", "localStorage", "getItem", "user", "isLoading", "state", "auth", "current", "scrollTo", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "className", "ref", "fallback", "_c", "$RefreshReg$"], "sources": ["D:/Softwares/insyt-care/insyt-care-web/src/layout/ProtectedRoutes/index.jsx"], "sourcesContent": ["import usePageIsOverflow from \"@hooks/usePageIsOverflow\";\r\nimport useWindowSize from \"@hooks/useWindowSize\";\r\nimport Panel from \"@layout/Panel\";\r\nimport Sidebar from \"@layout/Sidebar\";\r\nimport ScrollProgress from \"@ui/ScrollProgress\";\r\nimport React, { useEffect, useRef, useState } from \"react\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { Navigate, Outlet } from \"react-router-dom\";\r\nimport { ThemeProvider as MuiThemeProvider } from \"@mui/material/styles\";\r\nimport { theme } from \"@styles/mui-theme\";\r\nimport { Suspense } from \"react\";\r\nimport WidgetsLoader from \"@components/WidgetsLoader\";\r\n\r\nconst ProtectedRoutes = () => {\r\n  const appRef = useRef(null);\r\n  const isOverflow = usePageIsOverflow();\r\n  const userId = localStorage.getItem(\"userId\");\r\n  const { user, isLoading } = useSelector((state) => state.auth);\r\n\r\n  useEffect(() => {\r\n    if (appRef.current) {\r\n      appRef.current.scrollTo(0, 0);\r\n    }\r\n  }, []);\r\n\r\n  // If we have userId but no user data and not loading, redirect to login\r\n  if (userId && !user && !isLoading) {\r\n    return <Navigate to=\"/login\" />;\r\n  }\r\n\r\n  // If we have userId and user data, show protected content\r\n  if (userId && user) {\r\n    return (\r\n      <>\r\n        <MuiThemeProvider theme={theme}>\r\n          <div className=\"app\" ref={appRef}>\r\n            {isOverflow ? <ScrollProgress /> : null}\r\n            <Sidebar />\r\n            <div className=\"app_content\">\r\n              <Panel />\r\n              <Suspense fallback={<WidgetsLoader />}>\r\n                <Outlet />\r\n              </Suspense>\r\n            </div>\r\n          </div>\r\n        </MuiThemeProvider>\r\n      </>\r\n    );\r\n  }\r\n\r\n  // If we have userId and still loading, show loading state\r\n  if (userId && isLoading) {\r\n    return <WidgetsLoader />;\r\n  }\r\n\r\n  // Default case: no userId or authentication failed, redirect to login\r\n  return <Navigate to=\"/login\" />;\r\n};\r\n\r\nexport default ProtectedRoutes;\r\n"], "mappings": ";;AAAA,OAAOA,iBAAiB,MAAM,0BAA0B;AACxD,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,OAAOC,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,QAAQ,EAAEC,MAAM,QAAQ,kBAAkB;AACnD,SAASC,aAAa,IAAIC,gBAAgB,QAAQ,sBAAsB;AACxE,SAASC,KAAK,QAAQ,mBAAmB;AACzC,SAASC,QAAQ,QAAQ,OAAO;AAChC,OAAOC,aAAa,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,MAAM,GAAGhB,MAAM,CAAC,IAAI,CAAC;EAC3B,MAAMiB,UAAU,GAAGxB,iBAAiB,CAAC,CAAC;EACtC,MAAMyB,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;EAC7C,MAAM;IAAEC,IAAI;IAAEC;EAAU,CAAC,GAAGpB,WAAW,CAAEqB,KAAK,IAAKA,KAAK,CAACC,IAAI,CAAC;EAE9DzB,SAAS,CAAC,MAAM;IACd,IAAIiB,MAAM,CAACS,OAAO,EAAE;MAClBT,MAAM,CAACS,OAAO,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IAC/B;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAIR,MAAM,IAAI,CAACG,IAAI,IAAI,CAACC,SAAS,EAAE;IACjC,oBAAOX,OAAA,CAACR,QAAQ;MAACwB,EAAE,EAAC;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACjC;;EAEA;EACA,IAAIb,MAAM,IAAIG,IAAI,EAAE;IAClB,oBACEV,OAAA,CAAAE,SAAA;MAAAmB,QAAA,eACErB,OAAA,CAACL,gBAAgB;QAACC,KAAK,EAAEA,KAAM;QAAAyB,QAAA,eAC7BrB,OAAA;UAAKsB,SAAS,EAAC,KAAK;UAACC,GAAG,EAAElB,MAAO;UAAAgB,QAAA,GAC9Bf,UAAU,gBAAGN,OAAA,CAACd,cAAc;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG,IAAI,eACvCpB,OAAA,CAACf,OAAO;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACXpB,OAAA;YAAKsB,SAAS,EAAC,aAAa;YAAAD,QAAA,gBAC1BrB,OAAA,CAAChB,KAAK;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACTpB,OAAA,CAACH,QAAQ;cAAC2B,QAAQ,eAAExB,OAAA,CAACF,aAAa;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAC,QAAA,eACpCrB,OAAA,CAACP,MAAM;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU;IAAC,gBACnB,CAAC;EAEP;;EAEA;EACA,IAAIb,MAAM,IAAII,SAAS,EAAE;IACvB,oBAAOX,OAAA,CAACF,aAAa;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC1B;;EAEA;EACA,oBAAOpB,OAAA,CAACR,QAAQ;IAACwB,EAAE,EAAC;EAAQ;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACjC,CAAC;AAAChB,EAAA,CA5CID,eAAe;EAAA,QAEArB,iBAAiB,EAERS,WAAW;AAAA;AAAAkC,EAAA,GAJnCtB,eAAe;AA8CrB,eAAeA,eAAe;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}