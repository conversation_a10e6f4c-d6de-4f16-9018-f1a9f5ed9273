{"ast": null, "code": "var _jsxFileName = \"D:\\\\Softwares\\\\insyt-care\\\\insyt-care-web\\\\src\\\\AppLayout.jsx\",\n  _s = $RefreshSig$();\n// utils\nimport { lazy } from \"react\";\n\n// components\nimport { Navigate, Route, Routes } from \"react-router-dom\";\n\n// hooks\nimport { useEffect } from \"react\";\nimport { auth, messaging } from \"config/firebase.config\";\nimport { onAuthStateChanged } from \"firebase/auth\";\nimport ProtectedRoutes from \"@layout/ProtectedRoutes\";\nimport PublicRoutes from \"@layout/PublicRoutes\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { getLoggedInUser, updateAuthUserAction } from \"@store/slices/auth\";\nimport { getAllCaregiversOfNurse, getAllPatientsOfNurse, getAllUsers } from \"@store/slices/users\";\nimport { getAllChats, setupChatsRealtime, cleanupChatListeners } from \"@store/slices/chats\";\nimport { getAllAppointments, getAllAppointmentsOfNurse } from \"@store/slices/appointments\";\nimport { getAllTasks } from \"@store/slices/tasks\";\nimport { getNotificationsOfAdmin, getNotificationsOfNurse } from \"@store/slices/notifications\";\nimport { onMessage } from \"firebase/messaging\";\nimport { subscribeToNotifications, cleanupSubscriptions } from \"@store/slices/notifications\";\n\n// pages\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Dashboard = /*#__PURE__*/lazy(_c = () => import(\"@pages/Dashboard\"));\n_c2 = Dashboard;\nconst DashboardA = /*#__PURE__*/lazy(_c3 = () => import(\"@pages/DashboardA\"));\n_c4 = DashboardA;\nconst DashboardB = /*#__PURE__*/lazy(_c5 = () => import(\"@pages/DashboardB\"));\n_c6 = DashboardB;\nconst DashboardC = /*#__PURE__*/lazy(_c7 = () => import(\"@pages/DashboardC\"));\n_c8 = DashboardC;\nconst DashboardD = /*#__PURE__*/lazy(_c9 = () => import(\"@pages/DashboardD\"));\n_c0 = DashboardD;\nconst DashboardE = /*#__PURE__*/lazy(_c1 = () => import(\"@pages/DashboardE\"));\n_c10 = DashboardE;\nconst DashboardF = /*#__PURE__*/lazy(_c11 = () => import(\"@pages/DashboardF\"));\n_c12 = DashboardF;\nconst DashboardG = /*#__PURE__*/lazy(_c13 = () => import(\"@pages/DashboardG\"));\n_c14 = DashboardG;\nconst DashboardH = /*#__PURE__*/lazy(_c15 = () => import(\"@pages/DashboardH\"));\n_c16 = DashboardH;\nconst DashboardI = /*#__PURE__*/lazy(_c17 = () => import(\"@pages/DashboardI\"));\n_c18 = DashboardI;\nconst DashboardJ = /*#__PURE__*/lazy(_c19 = () => import(\"@pages/DashboardJ\"));\n_c20 = DashboardJ;\nconst DashboardK = /*#__PURE__*/lazy(_c21 = () => import(\"@pages/DashboardK\"));\n_c22 = DashboardK;\nconst DoctorAppointments = /*#__PURE__*/lazy(_c23 = () => import(\"@pages/DoctorAppointments\"));\n_c24 = DoctorAppointments;\nconst Appointments = /*#__PURE__*/lazy(_c25 = () => import(\"@pages/Appointments\"));\n_c26 = Appointments;\nconst AppointmentDetails = /*#__PURE__*/lazy(_c27 = () => import(\"@pages/AppointmentDetails\"));\n_c28 = AppointmentDetails;\nconst Patients = /*#__PURE__*/lazy(_c29 = () => import(\"@pages/Patients\"));\n_c30 = Patients;\nconst PatientDetails = /*#__PURE__*/lazy(_c31 = () => import(\"@pages/PatientDetails\"));\n_c32 = PatientDetails;\nconst StaffDetails = /*#__PURE__*/lazy(_c33 = () => import(\"@pages/StaffDetails\"));\n_c34 = StaffDetails;\nconst AddPatient = /*#__PURE__*/lazy(_c35 = () => import(\"@pages/AddPatient\"));\n_c36 = AddPatient;\nconst Tests = /*#__PURE__*/lazy(_c37 = () => import(\"@pages/Tests\"));\n_c38 = Tests;\nconst Staff = /*#__PURE__*/lazy(_c39 = () => import(\"@pages/Staff\"));\n_c40 = Staff;\nconst StaffMessenger = /*#__PURE__*/lazy(_c41 = () => import(\"@pages/DoctorMessenger\"));\n_c42 = StaffMessenger;\nconst PatientMessenger = /*#__PURE__*/lazy(_c43 = () => import(\"@pages/PatientMessenger\"));\n_c44 = PatientMessenger;\nconst DoctorsReviews = /*#__PURE__*/lazy(_c45 = () => import(\"@pages/DoctorsReviews\"));\n_c46 = DoctorsReviews;\nconst PatientReviews = /*#__PURE__*/lazy(_c47 = () => import(\"@pages/PatientReviews\"));\n_c48 = PatientReviews;\nconst Finances = /*#__PURE__*/lazy(_c49 = () => import(\"@pages/Finances\"));\n_c50 = Finances;\nconst Settings = /*#__PURE__*/lazy(_c51 = () => import(\"@pages/Settings\"));\n_c52 = Settings;\nconst PageNotFound = /*#__PURE__*/lazy(_c53 = () => import(\"@pages/PageNotFound\"));\n_c54 = PageNotFound;\nconst Login = /*#__PURE__*/lazy(_c55 = () => import(\"@pages/Login\"));\n_c56 = Login;\nconst Nurses = /*#__PURE__*/lazy(_c57 = () => import(\"@pages/Nurses\"));\n_c58 = Nurses;\nconst AddUser = /*#__PURE__*/lazy(_c59 = () => import(\"@pages/AddUser\"));\n_c60 = AddUser;\nconst AddCaregiver = /*#__PURE__*/lazy(_c61 = () => import(\"@pages/AddCaregiver\"));\n_c62 = AddCaregiver;\nconst Caregivers = /*#__PURE__*/lazy(_c63 = () => import(\"@pages/Caregivers\"));\n_c64 = Caregivers;\nconst Tasks = /*#__PURE__*/lazy(_c65 = () => import(\"@pages/Tasks\"));\n_c66 = Tasks;\nconst Notifications = /*#__PURE__*/lazy(_c67 = () => import(\"@pages/Notifications\"));\n_c68 = Notifications;\nconst AppLayout = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    user\n  } = useSelector(state => state.auth);\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, currentUser => {\n      if (currentUser) {\n        dispatch(getLoggedInUser(currentUser.uid));\n      } else if (!currentUser) {\n        // User is null - clear authentication state and localStorage\n        console.log(\"current.user >\", currentUser);\n        localStorage.removeItem(\"userId\");\n        localStorage.removeItem(\"access_token\");\n        dispatch(updateAuthUserAction(null));\n      }\n    });\n    return () => unsubscribe();\n  }, [dispatch]);\n  useEffect(() => {\n    if (user) {\n      // Set up real-time notification subscription for all users\n      dispatch(subscribeToNotifications({\n        userRole: user.role,\n        userId: user.id\n      }));\n      if ((user === null || user === void 0 ? void 0 : user.role) === \"ADMIN\") {\n        dispatch(getAllUsers());\n        dispatch(getAllAppointments());\n        dispatch(getAllTasks());\n      }\n      if ((user === null || user === void 0 ? void 0 : user.role) === \"NURSE\") {\n        dispatch(getAllChats(user === null || user === void 0 ? void 0 : user.id));\n        dispatch(getAllCaregiversOfNurse());\n        dispatch(getAllPatientsOfNurse(user === null || user === void 0 ? void 0 : user.id));\n        dispatch(getAllAppointmentsOfNurse(user === null || user === void 0 ? void 0 : user.id));\n        dispatch(getAllTasks());\n      }\n    }\n\n    // Cleanup function\n    return () => {\n      dispatch(cleanupSubscriptions());\n    };\n  }, [user]);\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.id) {\n      // Setup real-time listeners for chats\n      dispatch(setupChatsRealtime(user.id));\n\n      // Cleanup function\n      return () => {\n        dispatch(cleanupChatListeners());\n      };\n    }\n  }, [user === null || user === void 0 ? void 0 : user.id, dispatch]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        element: /*#__PURE__*/_jsxDEV(PublicRoutes, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 25\n        }, this),\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoutes, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 25\n        }, this),\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/dashboard\",\n          element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/nurses\",\n          element: /*#__PURE__*/_jsxDEV(Nurses, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 42\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/staff\",\n          element: /*#__PURE__*/_jsxDEV(Staff, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/caregivers\",\n          element: /*#__PURE__*/_jsxDEV(Caregivers, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/add_staff\",\n          element: /*#__PURE__*/_jsxDEV(AddUser, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/add_client\",\n          element: /*#__PURE__*/_jsxDEV(AddPatient, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/settings\",\n          element: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/clients\",\n          element: /*#__PURE__*/_jsxDEV(Patients, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/clients/:clientId\",\n          element: /*#__PURE__*/_jsxDEV(PatientDetails, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 53\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/staff/:staffId\",\n          element: /*#__PURE__*/_jsxDEV(StaffDetails, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 50\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/appointments\",\n          element: /*#__PURE__*/_jsxDEV(Appointments, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 48\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/appointments/:appointmentId\",\n          element: /*#__PURE__*/_jsxDEV(AppointmentDetails, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 63\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/staff_messenger\",\n          element: /*#__PURE__*/_jsxDEV(StaffMessenger, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 51\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/client_messenger\",\n          element: /*#__PURE__*/_jsxDEV(PatientMessenger, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 52\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/nurse_chats\",\n          element: /*#__PURE__*/_jsxDEV(PatientMessenger, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 47\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/tasks\",\n          element: /*#__PURE__*/_jsxDEV(Tasks, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/notifications\",\n          element: /*#__PURE__*/_jsxDEV(Notifications, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 49\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n};\n_s(AppLayout, \"0ZCEG8ggXXgI5xhFijjLCRpa1UQ=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c69 = AppLayout;\nexport default AppLayout;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31, _c32, _c33, _c34, _c35, _c36, _c37, _c38, _c39, _c40, _c41, _c42, _c43, _c44, _c45, _c46, _c47, _c48, _c49, _c50, _c51, _c52, _c53, _c54, _c55, _c56, _c57, _c58, _c59, _c60, _c61, _c62, _c63, _c64, _c65, _c66, _c67, _c68, _c69;\n$RefreshReg$(_c, \"Dashboard$lazy\");\n$RefreshReg$(_c2, \"Dashboard\");\n$RefreshReg$(_c3, \"DashboardA$lazy\");\n$RefreshReg$(_c4, \"DashboardA\");\n$RefreshReg$(_c5, \"DashboardB$lazy\");\n$RefreshReg$(_c6, \"DashboardB\");\n$RefreshReg$(_c7, \"DashboardC$lazy\");\n$RefreshReg$(_c8, \"DashboardC\");\n$RefreshReg$(_c9, \"DashboardD$lazy\");\n$RefreshReg$(_c0, \"DashboardD\");\n$RefreshReg$(_c1, \"DashboardE$lazy\");\n$RefreshReg$(_c10, \"DashboardE\");\n$RefreshReg$(_c11, \"DashboardF$lazy\");\n$RefreshReg$(_c12, \"DashboardF\");\n$RefreshReg$(_c13, \"DashboardG$lazy\");\n$RefreshReg$(_c14, \"DashboardG\");\n$RefreshReg$(_c15, \"DashboardH$lazy\");\n$RefreshReg$(_c16, \"DashboardH\");\n$RefreshReg$(_c17, \"DashboardI$lazy\");\n$RefreshReg$(_c18, \"DashboardI\");\n$RefreshReg$(_c19, \"DashboardJ$lazy\");\n$RefreshReg$(_c20, \"DashboardJ\");\n$RefreshReg$(_c21, \"DashboardK$lazy\");\n$RefreshReg$(_c22, \"DashboardK\");\n$RefreshReg$(_c23, \"DoctorAppointments$lazy\");\n$RefreshReg$(_c24, \"DoctorAppointments\");\n$RefreshReg$(_c25, \"Appointments$lazy\");\n$RefreshReg$(_c26, \"Appointments\");\n$RefreshReg$(_c27, \"AppointmentDetails$lazy\");\n$RefreshReg$(_c28, \"AppointmentDetails\");\n$RefreshReg$(_c29, \"Patients$lazy\");\n$RefreshReg$(_c30, \"Patients\");\n$RefreshReg$(_c31, \"PatientDetails$lazy\");\n$RefreshReg$(_c32, \"PatientDetails\");\n$RefreshReg$(_c33, \"StaffDetails$lazy\");\n$RefreshReg$(_c34, \"StaffDetails\");\n$RefreshReg$(_c35, \"AddPatient$lazy\");\n$RefreshReg$(_c36, \"AddPatient\");\n$RefreshReg$(_c37, \"Tests$lazy\");\n$RefreshReg$(_c38, \"Tests\");\n$RefreshReg$(_c39, \"Staff$lazy\");\n$RefreshReg$(_c40, \"Staff\");\n$RefreshReg$(_c41, \"StaffMessenger$lazy\");\n$RefreshReg$(_c42, \"StaffMessenger\");\n$RefreshReg$(_c43, \"PatientMessenger$lazy\");\n$RefreshReg$(_c44, \"PatientMessenger\");\n$RefreshReg$(_c45, \"DoctorsReviews$lazy\");\n$RefreshReg$(_c46, \"DoctorsReviews\");\n$RefreshReg$(_c47, \"PatientReviews$lazy\");\n$RefreshReg$(_c48, \"PatientReviews\");\n$RefreshReg$(_c49, \"Finances$lazy\");\n$RefreshReg$(_c50, \"Finances\");\n$RefreshReg$(_c51, \"Settings$lazy\");\n$RefreshReg$(_c52, \"Settings\");\n$RefreshReg$(_c53, \"PageNotFound$lazy\");\n$RefreshReg$(_c54, \"PageNotFound\");\n$RefreshReg$(_c55, \"Login$lazy\");\n$RefreshReg$(_c56, \"Login\");\n$RefreshReg$(_c57, \"Nurses$lazy\");\n$RefreshReg$(_c58, \"Nurses\");\n$RefreshReg$(_c59, \"AddUser$lazy\");\n$RefreshReg$(_c60, \"AddUser\");\n$RefreshReg$(_c61, \"AddCaregiver$lazy\");\n$RefreshReg$(_c62, \"AddCaregiver\");\n$RefreshReg$(_c63, \"Caregivers$lazy\");\n$RefreshReg$(_c64, \"Caregivers\");\n$RefreshReg$(_c65, \"Tasks$lazy\");\n$RefreshReg$(_c66, \"Tasks\");\n$RefreshReg$(_c67, \"Notifications$lazy\");\n$RefreshReg$(_c68, \"Notifications\");\n$RefreshReg$(_c69, \"AppLayout\");", "map": {"version": 3, "names": ["lazy", "Navigate", "Route", "Routes", "useEffect", "auth", "messaging", "onAuthStateChanged", "ProtectedRoutes", "PublicRoutes", "useDispatch", "useSelector", "getLoggedInUser", "updateAuthUserAction", "getAllCaregiversOfNurse", "getAllPatientsOfNurse", "getAllUsers", "getAllChats", "setupChatsRealtime", "cleanupChatListeners", "getAllAppointments", "getAllAppointmentsOfNurse", "getAllTasks", "getNotificationsOfAdmin", "getNotificationsOfNurse", "onMessage", "subscribeToNotifications", "cleanupSubscriptions", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Dashboard", "_c", "_c2", "DashboardA", "_c3", "_c4", "DashboardB", "_c5", "_c6", "DashboardC", "_c7", "_c8", "DashboardD", "_c9", "_c0", "DashboardE", "_c1", "_c10", "DashboardF", "_c11", "_c12", "DashboardG", "_c13", "_c14", "DashboardH", "_c15", "_c16", "DashboardI", "_c17", "_c18", "DashboardJ", "_c19", "_c20", "DashboardK", "_c21", "_c22", "DoctorAppointments", "_c23", "_c24", "Appointments", "_c25", "_c26", "AppointmentDetails", "_c27", "_c28", "Patients", "_c29", "_c30", "PatientDetails", "_c31", "_c32", "StaffDetails", "_c33", "_c34", "AddPatient", "_c35", "_c36", "Tests", "_c37", "_c38", "Staff", "_c39", "_c40", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c41", "_c42", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_c43", "_c44", "DoctorsReviews", "_c45", "_c46", "PatientReviews", "_c47", "_c48", "Finances", "_c49", "_c50", "Settings", "_c51", "_c52", "PageNotFound", "_c53", "_c54", "<PERSON><PERSON>", "_c55", "_c56", "Nurses", "_c57", "_c58", "AddUser", "_c59", "_c60", "AddCaregiver", "_c61", "_c62", "Caregivers", "_c63", "_c64", "Tasks", "_c65", "_c66", "Notifications", "_c67", "_c68", "AppLayout", "_s", "dispatch", "user", "state", "unsubscribe", "currentUser", "uid", "console", "log", "localStorage", "removeItem", "userRole", "role", "userId", "id", "children", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "to", "_c69", "$RefreshReg$"], "sources": ["D:/Softwares/insyt-care/insyt-care-web/src/AppLayout.jsx"], "sourcesContent": ["// utils\r\nimport { lazy } from \"react\";\r\n\r\n// components\r\nimport { Navigate, Route, Routes } from \"react-router-dom\";\r\n\r\n// hooks\r\nimport { useEffect } from \"react\";\r\nimport { auth, messaging } from \"config/firebase.config\";\r\nimport { onAuthStateChanged } from \"firebase/auth\";\r\nimport ProtectedRoutes from \"@layout/ProtectedRoutes\";\r\nimport PublicRoutes from \"@layout/PublicRoutes\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { getLoggedInUser, updateAuthUserAction } from \"@store/slices/auth\";\r\nimport { getAllCaregiversOfNurse, getAllPatientsOfNurse, getAllUsers } from \"@store/slices/users\";\r\nimport { getAllChats, setupChatsRealtime, cleanupChatListeners } from \"@store/slices/chats\";\r\nimport { getAllAppointments, getAllAppointmentsOfNurse } from \"@store/slices/appointments\";\r\nimport { getAllTasks } from \"@store/slices/tasks\";\r\nimport { getNotificationsOfAdmin, getNotificationsOfNurse } from \"@store/slices/notifications\";\r\nimport { onMessage } from \"firebase/messaging\";\r\nimport { subscribeToNotifications, cleanupSubscriptions } from \"@store/slices/notifications\";\r\n\r\n// pages\r\nconst Dashboard = lazy(() => import(\"@pages/Dashboard\"));\r\nconst DashboardA = lazy(() => import(\"@pages/DashboardA\"));\r\nconst DashboardB = lazy(() => import(\"@pages/DashboardB\"));\r\nconst DashboardC = lazy(() => import(\"@pages/DashboardC\"));\r\nconst DashboardD = lazy(() => import(\"@pages/DashboardD\"));\r\nconst DashboardE = lazy(() => import(\"@pages/DashboardE\"));\r\nconst DashboardF = lazy(() => import(\"@pages/DashboardF\"));\r\nconst DashboardG = lazy(() => import(\"@pages/DashboardG\"));\r\nconst DashboardH = lazy(() => import(\"@pages/DashboardH\"));\r\nconst DashboardI = lazy(() => import(\"@pages/DashboardI\"));\r\nconst DashboardJ = lazy(() => import(\"@pages/DashboardJ\"));\r\nconst DashboardK = lazy(() => import(\"@pages/DashboardK\"));\r\nconst DoctorAppointments = lazy(() => import(\"@pages/DoctorAppointments\"));\r\nconst Appointments = lazy(() => import(\"@pages/Appointments\"));\r\nconst AppointmentDetails = lazy(() => import(\"@pages/AppointmentDetails\"));\r\nconst Patients = lazy(() => import(\"@pages/Patients\"));\r\nconst PatientDetails = lazy(() => import(\"@pages/PatientDetails\"));\r\nconst StaffDetails = lazy(() => import(\"@pages/StaffDetails\"));\r\nconst AddPatient = lazy(() => import(\"@pages/AddPatient\"));\r\nconst Tests = lazy(() => import(\"@pages/Tests\"));\r\nconst Staff = lazy(() => import(\"@pages/Staff\"));\r\nconst StaffMessenger = lazy(() => import(\"@pages/DoctorMessenger\"));\r\nconst PatientMessenger = lazy(() => import(\"@pages/PatientMessenger\"));\r\nconst DoctorsReviews = lazy(() => import(\"@pages/DoctorsReviews\"));\r\nconst PatientReviews = lazy(() => import(\"@pages/PatientReviews\"));\r\nconst Finances = lazy(() => import(\"@pages/Finances\"));\r\nconst Settings = lazy(() => import(\"@pages/Settings\"));\r\nconst PageNotFound = lazy(() => import(\"@pages/PageNotFound\"));\r\nconst Login = lazy(() => import(\"@pages/Login\"));\r\nconst Nurses = lazy(() => import(\"@pages/Nurses\"));\r\nconst AddUser = lazy(() => import(\"@pages/AddUser\"));\r\nconst AddCaregiver = lazy(() => import(\"@pages/AddCaregiver\"));\r\nconst Caregivers = lazy(() => import(\"@pages/Caregivers\"));\r\nconst Tasks = lazy(() => import(\"@pages/Tasks\"));\r\nconst Notifications = lazy(() => import(\"@pages/Notifications\"));\r\n\r\nconst AppLayout = () => {\r\n  const dispatch = useDispatch();\r\n  const { user } = useSelector((state) => state.auth);\r\n\r\n  useEffect(() => {\r\n    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {\r\n      if (currentUser) {\r\n        dispatch(getLoggedInUser(currentUser.uid));\r\n      } else if (!currentUser) {\r\n        // User is null - clear authentication state and localStorage\r\n        console.log(\"current.user >\", currentUser);\r\n        localStorage.removeItem(\"userId\");\r\n        localStorage.removeItem(\"access_token\");\r\n        dispatch(updateAuthUserAction(null));\r\n      }\r\n    });\r\n\r\n    return () => unsubscribe();\r\n  }, [dispatch]);\r\n\r\n  useEffect(() => {\r\n    if (user) {      // Set up real-time notification subscription for all users\r\n      dispatch(subscribeToNotifications({ userRole: user.role, userId: user.id }));\r\n\r\n      if (user?.role === \"ADMIN\") {\r\n        dispatch(getAllUsers());\r\n        dispatch(getAllAppointments());\r\n        dispatch(getAllTasks());\r\n      }\r\n      if (user?.role === \"NURSE\") {\r\n        dispatch(getAllChats(user?.id));\r\n        dispatch(getAllCaregiversOfNurse());\r\n        dispatch(getAllPatientsOfNurse(user?.id));\r\n        dispatch(getAllAppointmentsOfNurse(user?.id));\r\n        dispatch(getAllTasks());\r\n      }\r\n    }\r\n\r\n    // Cleanup function\r\n    return () => {\r\n      dispatch(cleanupSubscriptions());\r\n    };\r\n  }, [user]);\r\n\r\n  useEffect(() => {\r\n    if (user?.id) {\r\n      // Setup real-time listeners for chats\r\n      dispatch(setupChatsRealtime(user.id));\r\n      \r\n      // Cleanup function\r\n      return () => {\r\n        dispatch(cleanupChatListeners());\r\n      };\r\n    }\r\n  }, [user?.id, dispatch]);\r\n\r\n  return (\r\n    <>\r\n      <Routes>\r\n        {/* PUBLIC ROUTES */}\r\n        <Route element={<PublicRoutes />}>\r\n          <Route path=\"/\" element={<Navigate to=\"/login\" />} />\r\n          <Route path=\"/login\" element={<Login />} />\r\n        </Route>\r\n\r\n        {/* PROTECTED ROUTES */}\r\n        <Route element={<ProtectedRoutes />}>\r\n          <Route path=\"/dashboard\" element={<Dashboard />} />\r\n          <Route path=\"/nurses\" element={<Nurses />} />\r\n          <Route path=\"/staff\" element={<Staff />} />\r\n          <Route path=\"/caregivers\" element={<Caregivers />} />\r\n          <Route path=\"/add_staff\" element={<AddUser />} />\r\n          <Route path=\"/add_client\" element={<AddPatient />} />\r\n          <Route path=\"/settings\" element={<Settings />} />\r\n          <Route path=\"/clients\" element={<Patients />} />\r\n          <Route path=\"/clients/:clientId\" element={<PatientDetails />} />\r\n          <Route path=\"/staff/:staffId\" element={<StaffDetails />} />\r\n          <Route path=\"/appointments\" element={<Appointments />} />\r\n          <Route path=\"/appointments/:appointmentId\" element={<AppointmentDetails />} />\r\n          <Route path=\"/staff_messenger\" element={<StaffMessenger />} />\r\n          <Route path=\"/client_messenger\" element={<PatientMessenger />} />\r\n          <Route path=\"/nurse_chats\" element={<PatientMessenger />} />\r\n          <Route path=\"/tasks\" element={<Tasks />} />\r\n          <Route path=\"/notifications\" element={<Notifications />} />\r\n        </Route>\r\n      </Routes>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AppLayout;\r\n"], "mappings": ";;AAAA;AACA,SAASA,IAAI,QAAQ,OAAO;;AAE5B;AACA,SAASC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,QAAQ,kBAAkB;;AAE1D;AACA,SAASC,SAAS,QAAQ,OAAO;AACjC,SAASC,IAAI,EAAEC,SAAS,QAAQ,wBAAwB;AACxD,SAASC,kBAAkB,QAAQ,eAAe;AAClD,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,eAAe,EAAEC,oBAAoB,QAAQ,oBAAoB;AAC1E,SAASC,uBAAuB,EAAEC,qBAAqB,EAAEC,WAAW,QAAQ,qBAAqB;AACjG,SAASC,WAAW,EAAEC,kBAAkB,EAAEC,oBAAoB,QAAQ,qBAAqB;AAC3F,SAASC,kBAAkB,EAAEC,yBAAyB,QAAQ,4BAA4B;AAC1F,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,uBAAuB,EAAEC,uBAAuB,QAAQ,6BAA6B;AAC9F,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,wBAAwB,EAAEC,oBAAoB,QAAQ,6BAA6B;;AAE5F;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,SAAS,gBAAGhC,IAAI,CAAAiC,EAAA,GAACA,CAAA,KAAM,MAAM,CAAC,kBAAkB,CAAC,CAAC;AAACC,GAAA,GAAnDF,SAAS;AACf,MAAMG,UAAU,gBAAGnC,IAAI,CAAAoC,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,GAAA,GAArDF,UAAU;AAChB,MAAMG,UAAU,gBAAGtC,IAAI,CAAAuC,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,GAAA,GAArDF,UAAU;AAChB,MAAMG,UAAU,gBAAGzC,IAAI,CAAA0C,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,GAAA,GAArDF,UAAU;AAChB,MAAMG,UAAU,gBAAG5C,IAAI,CAAA6C,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,GAAA,GAArDF,UAAU;AAChB,MAAMG,UAAU,gBAAG/C,IAAI,CAAAgD,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,IAAA,GAArDF,UAAU;AAChB,MAAMG,UAAU,gBAAGlD,IAAI,CAAAmD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,IAAA,GAArDF,UAAU;AAChB,MAAMG,UAAU,gBAAGrD,IAAI,CAAAsD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,IAAA,GAArDF,UAAU;AAChB,MAAMG,UAAU,gBAAGxD,IAAI,CAAAyD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,IAAA,GAArDF,UAAU;AAChB,MAAMG,UAAU,gBAAG3D,IAAI,CAAA4D,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,IAAA,GAArDF,UAAU;AAChB,MAAMG,UAAU,gBAAG9D,IAAI,CAAA+D,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,IAAA,GAArDF,UAAU;AAChB,MAAMG,UAAU,gBAAGjE,IAAI,CAAAkE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,IAAA,GAArDF,UAAU;AAChB,MAAMG,kBAAkB,gBAAGpE,IAAI,CAAAqE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC,CAAC;AAACC,IAAA,GAArEF,kBAAkB;AACxB,MAAMG,YAAY,gBAAGvE,IAAI,CAAAwE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAACC,IAAA,GAAzDF,YAAY;AAClB,MAAMG,kBAAkB,gBAAG1E,IAAI,CAAA2E,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC,CAAC;AAACC,IAAA,GAArEF,kBAAkB;AACxB,MAAMG,QAAQ,gBAAG7E,IAAI,CAAA8E,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,iBAAiB,CAAC,CAAC;AAACC,IAAA,GAAjDF,QAAQ;AACd,MAAMG,cAAc,gBAAGhF,IAAI,CAAAiF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,uBAAuB,CAAC,CAAC;AAACC,IAAA,GAA7DF,cAAc;AACpB,MAAMG,YAAY,gBAAGnF,IAAI,CAAAoF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAACC,IAAA,GAAzDF,YAAY;AAClB,MAAMG,UAAU,gBAAGtF,IAAI,CAAAuF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,IAAA,GAArDF,UAAU;AAChB,MAAMG,KAAK,gBAAGzF,IAAI,CAAA0F,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,cAAc,CAAC,CAAC;AAACC,IAAA,GAA3CF,KAAK;AACX,MAAMG,KAAK,gBAAG5F,IAAI,CAAA6F,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,cAAc,CAAC,CAAC;AAACC,IAAA,GAA3CF,KAAK;AACX,MAAMG,cAAc,gBAAG/F,IAAI,CAAAgG,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAAC;AAACC,IAAA,GAA9DF,cAAc;AACpB,MAAMG,gBAAgB,gBAAGlG,IAAI,CAAAmG,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC;AAACC,IAAA,GAAjEF,gBAAgB;AACtB,MAAMG,cAAc,gBAAGrG,IAAI,CAAAsG,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,uBAAuB,CAAC,CAAC;AAACC,IAAA,GAA7DF,cAAc;AACpB,MAAMG,cAAc,gBAAGxG,IAAI,CAAAyG,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,uBAAuB,CAAC,CAAC;AAACC,IAAA,GAA7DF,cAAc;AACpB,MAAMG,QAAQ,gBAAG3G,IAAI,CAAA4G,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,iBAAiB,CAAC,CAAC;AAACC,IAAA,GAAjDF,QAAQ;AACd,MAAMG,QAAQ,gBAAG9G,IAAI,CAAA+G,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,iBAAiB,CAAC,CAAC;AAACC,IAAA,GAAjDF,QAAQ;AACd,MAAMG,YAAY,gBAAGjH,IAAI,CAAAkH,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAACC,IAAA,GAAzDF,YAAY;AAClB,MAAMG,KAAK,gBAAGpH,IAAI,CAAAqH,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,cAAc,CAAC,CAAC;AAACC,IAAA,GAA3CF,KAAK;AACX,MAAMG,MAAM,gBAAGvH,IAAI,CAAAwH,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,eAAe,CAAC,CAAC;AAACC,IAAA,GAA7CF,MAAM;AACZ,MAAMG,OAAO,gBAAG1H,IAAI,CAAA2H,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,gBAAgB,CAAC,CAAC;AAACC,IAAA,GAA/CF,OAAO;AACb,MAAMG,YAAY,gBAAG7H,IAAI,CAAA8H,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAACC,IAAA,GAAzDF,YAAY;AAClB,MAAMG,UAAU,gBAAGhI,IAAI,CAAAiI,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,IAAA,GAArDF,UAAU;AAChB,MAAMG,KAAK,gBAAGnI,IAAI,CAAAoI,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,cAAc,CAAC,CAAC;AAACC,IAAA,GAA3CF,KAAK;AACX,MAAMG,aAAa,gBAAGtI,IAAI,CAAAuI,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAACC,IAAA,GAA3DF,aAAa;AAEnB,MAAMG,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAGjI,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkI;EAAK,CAAC,GAAGjI,WAAW,CAAEkI,KAAK,IAAKA,KAAK,CAACxI,IAAI,CAAC;EAEnDD,SAAS,CAAC,MAAM;IACd,MAAM0I,WAAW,GAAGvI,kBAAkB,CAACF,IAAI,EAAG0I,WAAW,IAAK;MAC5D,IAAIA,WAAW,EAAE;QACfJ,QAAQ,CAAC/H,eAAe,CAACmI,WAAW,CAACC,GAAG,CAAC,CAAC;MAC5C,CAAC,MAAM,IAAI,CAACD,WAAW,EAAE;QACvB;QACAE,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEH,WAAW,CAAC;QAC1CI,YAAY,CAACC,UAAU,CAAC,QAAQ,CAAC;QACjCD,YAAY,CAACC,UAAU,CAAC,cAAc,CAAC;QACvCT,QAAQ,CAAC9H,oBAAoB,CAAC,IAAI,CAAC,CAAC;MACtC;IACF,CAAC,CAAC;IAEF,OAAO,MAAMiI,WAAW,CAAC,CAAC;EAC5B,CAAC,EAAE,CAACH,QAAQ,CAAC,CAAC;EAEdvI,SAAS,CAAC,MAAM;IACd,IAAIwI,IAAI,EAAE;MAAO;MACfD,QAAQ,CAACjH,wBAAwB,CAAC;QAAE2H,QAAQ,EAAET,IAAI,CAACU,IAAI;QAAEC,MAAM,EAAEX,IAAI,CAACY;MAAG,CAAC,CAAC,CAAC;MAE5E,IAAI,CAAAZ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,IAAI,MAAK,OAAO,EAAE;QAC1BX,QAAQ,CAAC3H,WAAW,CAAC,CAAC,CAAC;QACvB2H,QAAQ,CAACvH,kBAAkB,CAAC,CAAC,CAAC;QAC9BuH,QAAQ,CAACrH,WAAW,CAAC,CAAC,CAAC;MACzB;MACA,IAAI,CAAAsH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,IAAI,MAAK,OAAO,EAAE;QAC1BX,QAAQ,CAAC1H,WAAW,CAAC2H,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,EAAE,CAAC,CAAC;QAC/Bb,QAAQ,CAAC7H,uBAAuB,CAAC,CAAC,CAAC;QACnC6H,QAAQ,CAAC5H,qBAAqB,CAAC6H,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,EAAE,CAAC,CAAC;QACzCb,QAAQ,CAACtH,yBAAyB,CAACuH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,EAAE,CAAC,CAAC;QAC7Cb,QAAQ,CAACrH,WAAW,CAAC,CAAC,CAAC;MACzB;IACF;;IAEA;IACA,OAAO,MAAM;MACXqH,QAAQ,CAAChH,oBAAoB,CAAC,CAAC,CAAC;IAClC,CAAC;EACH,CAAC,EAAE,CAACiH,IAAI,CAAC,CAAC;EAEVxI,SAAS,CAAC,MAAM;IACd,IAAIwI,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEY,EAAE,EAAE;MACZ;MACAb,QAAQ,CAACzH,kBAAkB,CAAC0H,IAAI,CAACY,EAAE,CAAC,CAAC;;MAErC;MACA,OAAO,MAAM;QACXb,QAAQ,CAACxH,oBAAoB,CAAC,CAAC,CAAC;MAClC,CAAC;IACH;EACF,CAAC,EAAE,CAACyH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,EAAE,EAAEb,QAAQ,CAAC,CAAC;EAExB,oBACE9G,OAAA,CAAAE,SAAA;IAAA0H,QAAA,eACE5H,OAAA,CAAC1B,MAAM;MAAAsJ,QAAA,gBAEL5H,OAAA,CAAC3B,KAAK;QAACwJ,OAAO,eAAE7H,OAAA,CAACpB,YAAY;UAAAkJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAL,QAAA,gBAC/B5H,OAAA,CAAC3B,KAAK;UAAC6J,IAAI,EAAC,GAAG;UAACL,OAAO,eAAE7H,OAAA,CAAC5B,QAAQ;YAAC+J,EAAE,EAAC;UAAQ;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrDjI,OAAA,CAAC3B,KAAK;UAAC6J,IAAI,EAAC,QAAQ;UAACL,OAAO,eAAE7H,OAAA,CAACuF,KAAK;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eAGRjI,OAAA,CAAC3B,KAAK;QAACwJ,OAAO,eAAE7H,OAAA,CAACrB,eAAe;UAAAmJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAL,QAAA,gBAClC5H,OAAA,CAAC3B,KAAK;UAAC6J,IAAI,EAAC,YAAY;UAACL,OAAO,eAAE7H,OAAA,CAACG,SAAS;YAAA2H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnDjI,OAAA,CAAC3B,KAAK;UAAC6J,IAAI,EAAC,SAAS;UAACL,OAAO,eAAE7H,OAAA,CAAC0F,MAAM;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7CjI,OAAA,CAAC3B,KAAK;UAAC6J,IAAI,EAAC,QAAQ;UAACL,OAAO,eAAE7H,OAAA,CAAC+D,KAAK;YAAA+D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3CjI,OAAA,CAAC3B,KAAK;UAAC6J,IAAI,EAAC,aAAa;UAACL,OAAO,eAAE7H,OAAA,CAACmG,UAAU;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrDjI,OAAA,CAAC3B,KAAK;UAAC6J,IAAI,EAAC,YAAY;UAACL,OAAO,eAAE7H,OAAA,CAAC6F,OAAO;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjDjI,OAAA,CAAC3B,KAAK;UAAC6J,IAAI,EAAC,aAAa;UAACL,OAAO,eAAE7H,OAAA,CAACyD,UAAU;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrDjI,OAAA,CAAC3B,KAAK;UAAC6J,IAAI,EAAC,WAAW;UAACL,OAAO,eAAE7H,OAAA,CAACiF,QAAQ;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjDjI,OAAA,CAAC3B,KAAK;UAAC6J,IAAI,EAAC,UAAU;UAACL,OAAO,eAAE7H,OAAA,CAACgD,QAAQ;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChDjI,OAAA,CAAC3B,KAAK;UAAC6J,IAAI,EAAC,oBAAoB;UAACL,OAAO,eAAE7H,OAAA,CAACmD,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChEjI,OAAA,CAAC3B,KAAK;UAAC6J,IAAI,EAAC,iBAAiB;UAACL,OAAO,eAAE7H,OAAA,CAACsD,YAAY;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3DjI,OAAA,CAAC3B,KAAK;UAAC6J,IAAI,EAAC,eAAe;UAACL,OAAO,eAAE7H,OAAA,CAAC0C,YAAY;YAAAoF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzDjI,OAAA,CAAC3B,KAAK;UAAC6J,IAAI,EAAC,8BAA8B;UAACL,OAAO,eAAE7H,OAAA,CAAC6C,kBAAkB;YAAAiF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9EjI,OAAA,CAAC3B,KAAK;UAAC6J,IAAI,EAAC,kBAAkB;UAACL,OAAO,eAAE7H,OAAA,CAACkE,cAAc;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9DjI,OAAA,CAAC3B,KAAK;UAAC6J,IAAI,EAAC,mBAAmB;UAACL,OAAO,eAAE7H,OAAA,CAACqE,gBAAgB;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjEjI,OAAA,CAAC3B,KAAK;UAAC6J,IAAI,EAAC,cAAc;UAACL,OAAO,eAAE7H,OAAA,CAACqE,gBAAgB;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5DjI,OAAA,CAAC3B,KAAK;UAAC6J,IAAI,EAAC,QAAQ;UAACL,OAAO,eAAE7H,OAAA,CAACsG,KAAK;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3CjI,OAAA,CAAC3B,KAAK;UAAC6J,IAAI,EAAC,gBAAgB;UAACL,OAAO,eAAE7H,OAAA,CAACyG,aAAa;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC,gBACT,CAAC;AAEP,CAAC;AAACpB,EAAA,CAxFID,SAAS;EAAA,QACI/H,WAAW,EACXC,WAAW;AAAA;AAAAsJ,IAAA,GAFxBxB,SAAS;AA0Ff,eAAeA,SAAS;AAAC,IAAAxG,EAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAyB,IAAA;AAAAC,YAAA,CAAAjI,EAAA;AAAAiI,YAAA,CAAAhI,GAAA;AAAAgI,YAAA,CAAA9H,GAAA;AAAA8H,YAAA,CAAA7H,GAAA;AAAA6H,YAAA,CAAA3H,GAAA;AAAA2H,YAAA,CAAA1H,GAAA;AAAA0H,YAAA,CAAAxH,GAAA;AAAAwH,YAAA,CAAAvH,GAAA;AAAAuH,YAAA,CAAArH,GAAA;AAAAqH,YAAA,CAAApH,GAAA;AAAAoH,YAAA,CAAAlH,GAAA;AAAAkH,YAAA,CAAAjH,IAAA;AAAAiH,YAAA,CAAA/G,IAAA;AAAA+G,YAAA,CAAA9G,IAAA;AAAA8G,YAAA,CAAA5G,IAAA;AAAA4G,YAAA,CAAA3G,IAAA;AAAA2G,YAAA,CAAAzG,IAAA;AAAAyG,YAAA,CAAAxG,IAAA;AAAAwG,YAAA,CAAAtG,IAAA;AAAAsG,YAAA,CAAArG,IAAA;AAAAqG,YAAA,CAAAnG,IAAA;AAAAmG,YAAA,CAAAlG,IAAA;AAAAkG,YAAA,CAAAhG,IAAA;AAAAgG,YAAA,CAAA/F,IAAA;AAAA+F,YAAA,CAAA7F,IAAA;AAAA6F,YAAA,CAAA5F,IAAA;AAAA4F,YAAA,CAAA1F,IAAA;AAAA0F,YAAA,CAAAzF,IAAA;AAAAyF,YAAA,CAAAvF,IAAA;AAAAuF,YAAA,CAAAtF,IAAA;AAAAsF,YAAA,CAAApF,IAAA;AAAAoF,YAAA,CAAAnF,IAAA;AAAAmF,YAAA,CAAAjF,IAAA;AAAAiF,YAAA,CAAAhF,IAAA;AAAAgF,YAAA,CAAA9E,IAAA;AAAA8E,YAAA,CAAA7E,IAAA;AAAA6E,YAAA,CAAA3E,IAAA;AAAA2E,YAAA,CAAA1E,IAAA;AAAA0E,YAAA,CAAAxE,IAAA;AAAAwE,YAAA,CAAAvE,IAAA;AAAAuE,YAAA,CAAArE,IAAA;AAAAqE,YAAA,CAAApE,IAAA;AAAAoE,YAAA,CAAAlE,IAAA;AAAAkE,YAAA,CAAAjE,IAAA;AAAAiE,YAAA,CAAA/D,IAAA;AAAA+D,YAAA,CAAA9D,IAAA;AAAA8D,YAAA,CAAA5D,IAAA;AAAA4D,YAAA,CAAA3D,IAAA;AAAA2D,YAAA,CAAAzD,IAAA;AAAAyD,YAAA,CAAAxD,IAAA;AAAAwD,YAAA,CAAAtD,IAAA;AAAAsD,YAAA,CAAArD,IAAA;AAAAqD,YAAA,CAAAnD,IAAA;AAAAmD,YAAA,CAAAlD,IAAA;AAAAkD,YAAA,CAAAhD,IAAA;AAAAgD,YAAA,CAAA/C,IAAA;AAAA+C,YAAA,CAAA7C,IAAA;AAAA6C,YAAA,CAAA5C,IAAA;AAAA4C,YAAA,CAAA1C,IAAA;AAAA0C,YAAA,CAAAzC,IAAA;AAAAyC,YAAA,CAAAvC,IAAA;AAAAuC,YAAA,CAAAtC,IAAA;AAAAsC,YAAA,CAAApC,IAAA;AAAAoC,YAAA,CAAAnC,IAAA;AAAAmC,YAAA,CAAAjC,IAAA;AAAAiC,YAAA,CAAAhC,IAAA;AAAAgC,YAAA,CAAA9B,IAAA;AAAA8B,YAAA,CAAA7B,IAAA;AAAA6B,YAAA,CAAA3B,IAAA;AAAA2B,YAAA,CAAA1B,IAAA;AAAA0B,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}