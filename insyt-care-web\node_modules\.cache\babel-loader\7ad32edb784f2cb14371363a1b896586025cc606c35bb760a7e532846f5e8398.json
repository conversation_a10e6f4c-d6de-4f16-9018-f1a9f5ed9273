{"ast": null, "code": "import { db } from \"config/firebase.config\";\nimport { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\nimport { doc, getDoc } from \"firebase/firestore\";\nimport { COLLECTIONS } from \"@constants/app\";\nconst initialState = {\n  error: null,\n  isLoading: false,\n  user: null\n};\n\n// THUNK TO GET LOGGED IN USER\nexport const getLoggedInUser = createAsyncThunk(\"auth/getLoggedInUser\", async (userId, {\n  rejectWithValue\n}) => {\n  if (userId) {\n    try {\n      const docSnapshot = await getDoc(doc(db, COLLECTIONS.USERS, userId));\n      if (!docSnapshot.exists()) {\n        return rejectWithValue(\"User data not doesn't exists\");\n      }\n      const user = docSnapshot.data();\n      return {\n        id: userId,\n        ...user\n      };\n    } catch (error) {\n      return rejectWithValue(error);\n    }\n  }\n});\nconst authSlice = createSlice({\n  name: \"auth\",\n  initialState,\n  reducers: {\n    updateAuthUserAction: (state, {\n      payload\n    }) => {\n      if (payload) {\n        state.user = {\n          ...state.user,\n          ...payload\n        };\n      } else if (payload === null) {\n        state.user = null;\n      }\n      return state;\n    }\n  },\n  extraReducers: builder => {\n    builder\n    // GET USER BY ID\n    .addCase(getLoggedInUser.pending, state => {\n      state.error = null;\n      state.isLoading = true;\n    }).addCase(getLoggedInUser.fulfilled, (state, {\n      payload\n    }) => {\n      state.user = payload;\n      state.isLoading = false;\n    }).addCase(getLoggedInUser.rejected, (state, {\n      payload\n    }) => {\n      state.isLoading = false;\n      state.error = payload;\n      state.user = null;\n      // Clear localStorage when user data fetch fails\n      localStorage.removeItem(\"userId\");\n      localStorage.removeItem(\"access_token\");\n    });\n  }\n});\nexport const {\n  updateAuthUserAction\n} = authSlice.actions;\nexport default authSlice;", "map": {"version": 3, "names": ["db", "createSlice", "createAsyncThunk", "doc", "getDoc", "COLLECTIONS", "initialState", "error", "isLoading", "user", "getLoggedInUser", "userId", "rejectWithValue", "docSnapshot", "USERS", "exists", "data", "id", "authSlice", "name", "reducers", "updateAuthUserAction", "state", "payload", "extraReducers", "builder", "addCase", "pending", "fulfilled", "rejected", "localStorage", "removeItem", "actions"], "sources": ["D:/Softwares/insyt-care/insyt-care-web/src/store/slices/auth.js"], "sourcesContent": ["import { db } from \"config/firebase.config\";\r\nimport { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\r\nimport { doc, getDoc } from \"firebase/firestore\";\r\nimport { COLLECTIONS } from \"@constants/app\";\r\n\r\nconst initialState = {\r\n  error: null,\r\n  isLoading: false,\r\n  user: null,\r\n};\r\n\r\n// THUNK TO GET LOGGED IN USER\r\nexport const getLoggedInUser = createAsyncThunk(\"auth/getLoggedInUser\", async (userId, { rejectWithValue }) => {\r\n  if (userId) {\r\n    try {\r\n      const docSnapshot = await getDoc(doc(db, COLLECTIONS.USERS, userId));\r\n      if (!docSnapshot.exists()) {\r\n        return rejectWithValue(\"User data not doesn't exists\");\r\n      }\r\n      const user = docSnapshot.data();\r\n      return {\r\n        id: userId,\r\n        ...user,\r\n      };\r\n    } catch (error) {\r\n      return rejectWithValue(error);\r\n    }\r\n  }\r\n});\r\n\r\nconst authSlice = createSlice({\r\n  name: \"auth\",\r\n  initialState,\r\n  reducers: {\r\n    updateAuthUserAction: (state, { payload }) => {\r\n      if (payload) {\r\n        state.user = { ...state.user, ...payload };\r\n      } else if (payload === null) {\r\n        state.user = null;\r\n      }\r\n      return state;\r\n    },\r\n  },\r\n  extraReducers: (builder) => {\r\n    builder\r\n      // GET USER BY ID\r\n      .addCase(getLoggedInUser.pending, (state) => {\r\n        state.error = null;\r\n        state.isLoading = true;\r\n      })\r\n      .addCase(getLoggedInUser.fulfilled, (state, { payload }) => {\r\n        state.user = payload;\r\n        state.isLoading = false;\r\n      })\r\n      .addCase(getLoggedInUser.rejected, (state, { payload }) => {\r\n        state.isLoading = false;\r\n        state.error = payload;\r\n        state.user = null;\r\n        // Clear localStorage when user data fetch fails\r\n        localStorage.removeItem(\"userId\");\r\n        localStorage.removeItem(\"access_token\");\r\n      });\r\n  },\r\n});\r\n\r\nexport const { updateAuthUserAction } = authSlice.actions;\r\n\r\nexport default authSlice;\r\n"], "mappings": "AAAA,SAASA,EAAE,QAAQ,wBAAwB;AAC3C,SAASC,WAAW,EAAEC,gBAAgB,QAAQ,kBAAkB;AAChE,SAASC,GAAG,EAAEC,MAAM,QAAQ,oBAAoB;AAChD,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,MAAMC,YAAY,GAAG;EACnBC,KAAK,EAAE,IAAI;EACXC,SAAS,EAAE,KAAK;EAChBC,IAAI,EAAE;AACR,CAAC;;AAED;AACA,OAAO,MAAMC,eAAe,GAAGR,gBAAgB,CAAC,sBAAsB,EAAE,OAAOS,MAAM,EAAE;EAAEC;AAAgB,CAAC,KAAK;EAC7G,IAAID,MAAM,EAAE;IACV,IAAI;MACF,MAAME,WAAW,GAAG,MAAMT,MAAM,CAACD,GAAG,CAACH,EAAE,EAAEK,WAAW,CAACS,KAAK,EAAEH,MAAM,CAAC,CAAC;MACpE,IAAI,CAACE,WAAW,CAACE,MAAM,CAAC,CAAC,EAAE;QACzB,OAAOH,eAAe,CAAC,8BAA8B,CAAC;MACxD;MACA,MAAMH,IAAI,GAAGI,WAAW,CAACG,IAAI,CAAC,CAAC;MAC/B,OAAO;QACLC,EAAE,EAAEN,MAAM;QACV,GAAGF;MACL,CAAC;IACH,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd,OAAOK,eAAe,CAACL,KAAK,CAAC;IAC/B;EACF;AACF,CAAC,CAAC;AAEF,MAAMW,SAAS,GAAGjB,WAAW,CAAC;EAC5BkB,IAAI,EAAE,MAAM;EACZb,YAAY;EACZc,QAAQ,EAAE;IACRC,oBAAoB,EAAEA,CAACC,KAAK,EAAE;MAAEC;IAAQ,CAAC,KAAK;MAC5C,IAAIA,OAAO,EAAE;QACXD,KAAK,CAACb,IAAI,GAAG;UAAE,GAAGa,KAAK,CAACb,IAAI;UAAE,GAAGc;QAAQ,CAAC;MAC5C,CAAC,MAAM,IAAIA,OAAO,KAAK,IAAI,EAAE;QAC3BD,KAAK,CAACb,IAAI,GAAG,IAAI;MACnB;MACA,OAAOa,KAAK;IACd;EACF,CAAC;EACDE,aAAa,EAAGC,OAAO,IAAK;IAC1BA;IACE;IAAA,CACCC,OAAO,CAAChB,eAAe,CAACiB,OAAO,EAAGL,KAAK,IAAK;MAC3CA,KAAK,CAACf,KAAK,GAAG,IAAI;MAClBe,KAAK,CAACd,SAAS,GAAG,IAAI;IACxB,CAAC,CAAC,CACDkB,OAAO,CAAChB,eAAe,CAACkB,SAAS,EAAE,CAACN,KAAK,EAAE;MAAEC;IAAQ,CAAC,KAAK;MAC1DD,KAAK,CAACb,IAAI,GAAGc,OAAO;MACpBD,KAAK,CAACd,SAAS,GAAG,KAAK;IACzB,CAAC,CAAC,CACDkB,OAAO,CAAChB,eAAe,CAACmB,QAAQ,EAAE,CAACP,KAAK,EAAE;MAAEC;IAAQ,CAAC,KAAK;MACzDD,KAAK,CAACd,SAAS,GAAG,KAAK;MACvBc,KAAK,CAACf,KAAK,GAAGgB,OAAO;MACrBD,KAAK,CAACb,IAAI,GAAG,IAAI;MACjB;MACAqB,YAAY,CAACC,UAAU,CAAC,QAAQ,CAAC;MACjCD,YAAY,CAACC,UAAU,CAAC,cAAc,CAAC;IACzC,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEV;AAAqB,CAAC,GAAGH,SAAS,CAACc,OAAO;AAEzD,eAAed,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}