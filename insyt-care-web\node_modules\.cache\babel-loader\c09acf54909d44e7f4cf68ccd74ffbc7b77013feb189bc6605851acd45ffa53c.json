{"ast": null, "code": "var _jsxFileName = \"D:\\\\Softwares\\\\insyt-care\\\\insyt-care-web\\\\src\\\\layout\\\\ProtectedRoutes\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport usePageIsOverflow from \"@hooks/usePageIsOverflow\";\nimport useWindowSize from \"@hooks/useWindowSize\";\nimport Panel from \"@layout/Panel\";\nimport Sidebar from \"@layout/Sidebar\";\nimport ScrollProgress from \"@ui/ScrollProgress\";\nimport React, { useEffect, useRef, useState } from \"react\";\nimport { useSelector } from \"react-redux\";\nimport { Navigate, Outlet } from \"react-router-dom\";\nimport { ThemeProvider as MuiThemeProvider } from \"@mui/material/styles\";\nimport { theme } from \"@styles/mui-theme\";\nimport { Suspense } from \"react\";\nimport WidgetsLoader from \"@components/WidgetsLoader\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProtectedRoutes = () => {\n  _s();\n  const appRef = useRef(null);\n  const isOverflow = usePageIsOverflow();\n  const userId = localStorage.getItem(\"userId\");\n  const {\n    user\n  } = useSelector(state => state.auth);\n  useEffect(() => {\n    if (appRef.current) {\n      appRef.current.scrollTo(0, 0);\n    }\n  }, []);\n\n  // Check both localStorage and Redux state for authentication\n  if (userId && user) {\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: /*#__PURE__*/_jsxDEV(MuiThemeProvider, {\n        theme: theme,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"app\",\n          ref: appRef,\n          children: [isOverflow ? /*#__PURE__*/_jsxDEV(ScrollProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 27\n          }, this) : null, /*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"app_content\",\n            children: [/*#__PURE__*/_jsxDEV(Panel, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Suspense, {\n              fallback: /*#__PURE__*/_jsxDEV(WidgetsLoader, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 36,\n                columnNumber: 35\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this)\n    }, void 0, false);\n  } else {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 12\n    }, this);\n  }\n};\n_s(ProtectedRoutes, \"V1V96IwqpIIqHVNmu6Esbq7fQfM=\", false, function () {\n  return [usePageIsOverflow, useSelector];\n});\n_c = ProtectedRoutes;\nexport default ProtectedRoutes;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoutes\");", "map": {"version": 3, "names": ["usePageIsOverflow", "useWindowSize", "Panel", "Sidebar", "ScrollProgress", "React", "useEffect", "useRef", "useState", "useSelector", "Navigate", "Outlet", "ThemeProvider", "MuiThemeProvider", "theme", "Suspense", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProtectedRoutes", "_s", "appRef", "isOverflow", "userId", "localStorage", "getItem", "user", "state", "auth", "current", "scrollTo", "children", "className", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fallback", "to", "_c", "$RefreshReg$"], "sources": ["D:/Softwares/insyt-care/insyt-care-web/src/layout/ProtectedRoutes/index.jsx"], "sourcesContent": ["import usePageIsOverflow from \"@hooks/usePageIsOverflow\";\r\nimport useWindowSize from \"@hooks/useWindowSize\";\r\nimport Panel from \"@layout/Panel\";\r\nimport Sidebar from \"@layout/Sidebar\";\r\nimport ScrollProgress from \"@ui/ScrollProgress\";\r\nimport React, { useEffect, useRef, useState } from \"react\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { Navigate, Outlet } from \"react-router-dom\";\r\nimport { ThemeProvider as MuiThemeProvider } from \"@mui/material/styles\";\r\nimport { theme } from \"@styles/mui-theme\";\r\nimport { Suspense } from \"react\";\r\nimport WidgetsLoader from \"@components/WidgetsLoader\";\r\n\r\nconst ProtectedRoutes = () => {\r\n  const appRef = useRef(null);\r\n  const isOverflow = usePageIsOverflow();\r\n  const userId = localStorage.getItem(\"userId\");\r\n  const { user } = useSelector((state) => state.auth);\r\n\r\n  useEffect(() => {\r\n    if (appRef.current) {\r\n      appRef.current.scrollTo(0, 0);\r\n    }\r\n  }, []);\r\n\r\n  // Check both localStorage and Redux state for authentication\r\n  if (userId && user) {\r\n    return (\r\n      <>\r\n        <MuiThemeProvider theme={theme}>\r\n          <div className=\"app\" ref={appRef}>\r\n            {isOverflow ? <ScrollProgress /> : null}\r\n            <Sidebar />\r\n            <div className=\"app_content\">\r\n              <Panel />\r\n              <Suspense fallback={<WidgetsLoader />}>\r\n                <Outlet />\r\n              </Suspense>\r\n            </div>\r\n          </div>\r\n        </MuiThemeProvider>\r\n      </>\r\n    );\r\n  } else {\r\n    return <Navigate to=\"/login\" />;\r\n  }\r\n};\r\n\r\nexport default ProtectedRoutes;\r\n"], "mappings": ";;AAAA,OAAOA,iBAAiB,MAAM,0BAA0B;AACxD,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,OAAOC,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,QAAQ,EAAEC,MAAM,QAAQ,kBAAkB;AACnD,SAASC,aAAa,IAAIC,gBAAgB,QAAQ,sBAAsB;AACxE,SAASC,KAAK,QAAQ,mBAAmB;AACzC,SAASC,QAAQ,QAAQ,OAAO;AAChC,OAAOC,aAAa,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,MAAM,GAAGhB,MAAM,CAAC,IAAI,CAAC;EAC3B,MAAMiB,UAAU,GAAGxB,iBAAiB,CAAC,CAAC;EACtC,MAAMyB,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;EAC7C,MAAM;IAAEC;EAAK,CAAC,GAAGnB,WAAW,CAAEoB,KAAK,IAAKA,KAAK,CAACC,IAAI,CAAC;EAEnDxB,SAAS,CAAC,MAAM;IACd,IAAIiB,MAAM,CAACQ,OAAO,EAAE;MAClBR,MAAM,CAACQ,OAAO,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IAC/B;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAIP,MAAM,IAAIG,IAAI,EAAE;IAClB,oBACEV,OAAA,CAAAE,SAAA;MAAAa,QAAA,eACEf,OAAA,CAACL,gBAAgB;QAACC,KAAK,EAAEA,KAAM;QAAAmB,QAAA,eAC7Bf,OAAA;UAAKgB,SAAS,EAAC,KAAK;UAACC,GAAG,EAAEZ,MAAO;UAAAU,QAAA,GAC9BT,UAAU,gBAAGN,OAAA,CAACd,cAAc;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG,IAAI,eACvCrB,OAAA,CAACf,OAAO;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACXrB,OAAA;YAAKgB,SAAS,EAAC,aAAa;YAAAD,QAAA,gBAC1Bf,OAAA,CAAChB,KAAK;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACTrB,OAAA,CAACH,QAAQ;cAACyB,QAAQ,eAAEtB,OAAA,CAACF,aAAa;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAN,QAAA,eACpCf,OAAA,CAACP,MAAM;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU;IAAC,gBACnB,CAAC;EAEP,CAAC,MAAM;IACL,oBAAOrB,OAAA,CAACR,QAAQ;MAAC+B,EAAE,EAAC;IAAQ;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACjC;AACF,CAAC;AAACjB,EAAA,CAjCID,eAAe;EAAA,QAEArB,iBAAiB,EAEnBS,WAAW;AAAA;AAAAiC,EAAA,GAJxBrB,eAAe;AAmCrB,eAAeA,eAAe;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}