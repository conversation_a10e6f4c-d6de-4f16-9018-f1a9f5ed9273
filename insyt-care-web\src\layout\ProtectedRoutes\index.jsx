import usePageIsOverflow from "@hooks/usePageIsOverflow";
import useWindowSize from "@hooks/useWindowSize";
import Panel from "@layout/Panel";
import Sidebar from "@layout/Sidebar";
import ScrollProgress from "@ui/ScrollProgress";
import React, { useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { Navigate, Outlet } from "react-router-dom";
import { ThemeProvider as MuiThemeProvider } from "@mui/material/styles";
import { theme } from "@styles/mui-theme";
import { Suspense } from "react";
import WidgetsLoader from "@components/WidgetsLoader";

const ProtectedRoutes = () => {
  const appRef = useRef(null);
  const isOverflow = usePageIsOverflow();
  const userId = localStorage.getItem("userId");
  const { user, isLoading } = useSelector((state) => state.auth);

  useEffect(() => {
    if (appRef.current) {
      appRef.current.scrollTo(0, 0);
    }
  }, []);

  // If we have userId but no user data and not loading, redirect to login
  if (userId && !user && !isLoading) {
    return <Navigate to="/login" />;
  }

  // If we have userId and user data, show protected content
  if (userId && user) {
    return (
      <>
        <MuiThemeProvider theme={theme}>
          <div className="app" ref={appRef}>
            {isOverflow ? <ScrollProgress /> : null}
            <Sidebar />
            <div className="app_content">
              <Panel />
              <Suspense fallback={<WidgetsLoader />}>
                <Outlet />
              </Suspense>
            </div>
          </div>
        </MuiThemeProvider>
      </>
    );
  }

  // If we have userId and still loading, show loading state
  if (userId && isLoading) {
    return <WidgetsLoader />;
  }

  // Default case: no userId or authentication failed, redirect to login
  return <Navigate to="/login" />;
};

export default ProtectedRoutes;
